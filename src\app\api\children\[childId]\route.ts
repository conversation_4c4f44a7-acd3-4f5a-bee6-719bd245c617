import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

export async function GET(
  request: Request,
  { params }: { params: { childId: string } }
) {
  const child = await prisma.child.findUnique({
    where: { id: params.childId },
  });
  return NextResponse.json(child);
}

export async function PUT(
  request: Request,
  { params }: { params: { childId: string } }
) {
  const data = await request.json();
  const child = await prisma.child.update({
    where: { id: params.childId },
    data,
  });
  return NextResponse.json(child);
}

export async function DELETE(
  request: Request,
  { params }: { params: { childId: string } }
) {
  await prisma.child.delete({
    where: { id: params.childId },
  });
  return new Response(null, { status: 204 });
}
