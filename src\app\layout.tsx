import type { Metadata } from 'next';
import './globals.css';
import AuthenticatedLayout from '@/components/layout/AuthenticatedLayout';
import { APP_NAME } from '@/lib/constants';
import ClientProviders from '@/providers/ClientProviders';

export const metadata: Metadata = {
  title: `${APP_NAME} - تنمية الطفولة المبكرة`,
  description: `${APP_NAME}: التقييم والتنمية للطفولة المبكرة بناءً على برنامج بورتيج.`,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl" suppressHydrationWarning>
      <body className="antialiased">
        <ClientProviders>
          <AuthenticatedLayout>{children}</AuthenticatedLayout>
        </ClientProviders>
      </body>
    </html>
  );
}
