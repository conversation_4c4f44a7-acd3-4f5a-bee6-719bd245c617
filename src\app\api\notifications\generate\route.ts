import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { generateAssessmentNotifications } from '@/lib/notifications';

// POST /api/notifications/generate - Generate assessment notifications (admin only)
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role (optional - you can remove this if you want all users to generate notifications)
    // const user = await prisma.user.findUnique({
    //   where: { id: session.user.id },
    //   select: { role: true },
    // });

    // if (user?.role !== 'admin') {
    //   return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 });
    // }

    const result = await generateAssessmentNotifications();

    return NextResponse.json({
      message: 'Assessment notifications generated successfully',
      created: result.created,
    });
  } catch (error) {
    console.error('Error generating notifications:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
