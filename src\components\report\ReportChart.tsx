"use client";

import type { Assessment } from '@/lib/types';
import { PORTAGE_CHECKLIST_DATA } from '@/lib/constants';
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import { formatDate } from '@/lib/utils';

interface ReportChartProps {
  assessment: Assessment;
  childName: string;
}

interface ChartDataPoint {
  dimensionName: string;
  "مهارات متقنة": number;
  "مهارات تحتاج تطوير": number;
}

// Helper function to get dimension name from skill ID
function getSkillDimensionName(skillId: string): string | undefined {
  for (const dimension of PORTAGE_CHECKLIST_DATA) {
    for (const subCategory of dimension.subCategories) {
      if (subCategory.skills && subCategory.skills.some(skill => skill.id === skillId)) {
        return dimension.name;
      }
    }
  }
  return undefined;
}

export default function ReportChart({ assessment, childName }: ReportChartProps) {
  // Add safety checks for assessment data
  if (!assessment || !assessment.assessedSkills || !Array.isArray(assessment.assessedSkills)) {
    return (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>ملخص أداء المهارات حسب البُعد</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-center py-4">لا توجد بيانات تقييم صالحة لعرض الرسم البياني.</p>
        </CardContent>
      </Card>
    );
  }

  const chartData = PORTAGE_CHECKLIST_DATA.map(dimension => {
    let achieved = 0;
    let needsDevelopment = 0;

    assessment.assessedSkills.forEach(assessedSkill => {
      if (!assessedSkill || !assessedSkill.skillId) return;
      
      const skillDimensionName = getSkillDimensionName(assessedSkill.skillId);
      if (skillDimensionName === dimension.name) {
        if (assessedSkill.status === 'yes') {
          achieved++;
        } else {
          needsDevelopment++;
        }
      }
    });
    return {
      dimensionName: (dimension.name || 'غير محدد').substring(0, 15) + '...', // Shorten names for better display
      "مهارات متقنة": achieved,
      "مهارات تحتاج تطوير": needsDevelopment,
    };
  }).filter(d => d["مهارات متقنة"] > 0 || d["مهارات تحتاج تطوير"] > 0);

  const chartConfig = {
    "مهارات متقنة": {
      label: "مهارات متقنة",
      color: "hsl(var(--chart-2))", 
    },
    "مهارات تحتاج تطوير": {
      label: "مهارات تحتاج تطوير",
      color: "hsl(var(--chart-5))", 
    },
  } satisfies ChartConfig;

  if (chartData.length === 0) {
    return (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>ملخص أداء المهارات حسب البُعد</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-center py-4">لا توجد بيانات كافية لعرض الرسم البياني لهذا التقييم.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mt-8 shadow-lg">
      <CardHeader>
        <CardTitle>ملخص أداء المهارات حسب البُعد</CardTitle>
        <CardDescription>
          رسم بياني يوضح تطور المهارات المتقنة والتي تحتاج إلى تطوير عبر الأبعاد المختلفة في تقييم {childName} بتاريخ {assessment.assessmentDate ? formatDate(assessment.assessmentDate) : 'غير محدد'}.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="min-h-[400px] w-full">
          <AreaChart 
            data={chartData} 
            margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
            <XAxis 
              dataKey="dimensionName"
              stroke="hsl(var(--muted-foreground))" 
              fontSize={11}
              angle={-45}
              textAnchor="end"
              height={60}
              interval={0}
            />
            <YAxis 
              stroke="hsl(var(--muted-foreground))" 
              fontSize={12}
              allowDecimals={false}
            />
            <ChartTooltip 
              content={<ChartTooltipContent indicator="dot" />} 
            />
            <ChartLegend content={<ChartLegendContent />} />
            <Area
              type="monotone"
              dataKey="مهارات متقنة"
              stackId="1"
              stroke="var(--color-مهارات متقنة)"
              fill="var(--color-مهارات متقنة)"
              fillOpacity={0.8}
            />
            <Area
              type="monotone"
              dataKey="مهارات تحتاج تطوير"
              stackId="1"
              stroke="var(--color-مهارات تحتاج تطوير)"
              fill="var(--color-مهارات تحتاج تطوير)"
              fillOpacity={0.8}
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
