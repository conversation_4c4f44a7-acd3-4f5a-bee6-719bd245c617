// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Child {
  id                  String                @id @default(cuid())
  childIdNumber       String                @unique
  name                String
  birthDate           DateTime
  enrollmentDate      DateTime
  specialistName      String
  avatarUrl           String?
  gender              String?
  caseStudyNotes      String?
  isDeleted           Boolean?              @default(false)
  assessments         Assessment[]
  learningPlans       LearningPlan[]
  comprehensiveReports ComprehensiveReport[]
  sessionNotes        SessionNote[]
  planNotes           PlanNote[]
  caseStudy           CaseStudyData?
}

model Assessment {
  id              String         @id @default(cuid())
  childId         String
  assessmentDate  DateTime
  assessedSkills  Json
  baselines       Json?
  ceilings        Json?
  child           Child          @relation(fields: [childId], references: [id])
  learningPlans   LearningPlan[]
  comprehensiveReports ComprehensiveReport[]
}

model LearningPlan {
  id            String     @id @default(cuid())
  childId       String
  assessmentId  String
  generatedDate DateTime
  planDetails   String     @db.Text
  suggestedDailyGoals String? @db.Text
  child         Child      @relation(fields: [childId], references: [id])
  assessment    Assessment @relation(fields: [assessmentId], references: [id])
}

model ComprehensiveReport {
  id                      String     @id @default(cuid())
  childId                 String
  assessmentId            String
  generatedDate           DateTime
  childName               String
  assessmentDate          DateTime
  childAgeInMonths        Int
  additionalFocus         String?
  executiveSummary        String     @db.Text
  strengths               String     @db.Text
  areasForDevelopment     String     @db.Text
  dataAnalysisHighlights  String     @db.Text
  actionableRecommendations String  @db.Text
  child                   Child      @relation(fields: [childId], references: [id])
  assessment              Assessment @relation(fields: [assessmentId], references: [id])
}

model Account {
  id                 String  @id @default(cuid())
  userId             String
  type               String
  provider           String
  providerAccountId  String
  refresh_token      String?  @db.Text
  access_token       String?  @db.Text
  expires_at         Int?
  token_type         String?
  scope              String?
  id_token           String?  @db.Text
  session_state      String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          String    @default("viewer")
  specialization String?
  accounts      Account[]
  sessions      Session[]
  notifications Notification[]
  notificationPreferences NotificationPreferences?
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model SessionNote {
  id            String   @id @default(cuid())
  date          DateTime
  goalDiscussed String
  attendees     String
  notes         String   @db.Text
  nextSteps     String?
  childId       String
  child         Child    @relation(fields: [childId], references: [id])
}

model PlanNote {
  id            String   @id @default(cuid())
  childId       String
  skillId       String?
  planType      String
  skillBehavior String?
  notes         String   @db.Text
  isGlobal      Boolean
  createdDate   DateTime
  lastModified  DateTime
  child         Child    @relation(fields: [childId], references: [id])
}

model CaseStudyData {
  id                      String   @id @default(cuid())
  childId                 String   @unique
  child                   Child    @relation(fields: [childId], references: [id])
  basicInfo               Json
  pregnancyAndBirthInfo   Json
  reinforcerResponseInfo  Json
}

model Notification {
  id          String   @id @default(cuid())
  userId      String
  type        String   // 'assessment_overdue', 'assessment_due_soon', 'service_complete', 'plan_created', 'session_note_added', 'system'
  title       String
  message     String   @db.Text
  isRead      Boolean  @default(false)
  createdAt   DateTime @default(now())
  readAt      DateTime?

  // Optional references to related entities
  childId     String?
  assessmentId String?
  planId      String?
  sessionNoteId String?

  // Metadata for additional context
  metadata    Json?

  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, isRead])
  @@index([userId, createdAt])
}

model NotificationPreferences {
  id                    String   @id @default(cuid())
  userId                String   @unique

  // In-app notification preferences
  enableInApp           Boolean  @default(true)
  assessmentReminders   Boolean  @default(true)
  serviceAlerts         Boolean  @default(true)
  planUpdates           Boolean  @default(true)
  sessionNoteAlerts     Boolean  @default(true)
  systemNotifications   Boolean  @default(true)

  // Email notification preferences
  enableEmail           Boolean  @default(false)
  emailAssessmentReminders Boolean @default(false)
  emailServiceAlerts    Boolean  @default(false)
  emailPlanUpdates      Boolean  @default(false)
  emailSessionNoteAlerts Boolean @default(false)
  emailSystemNotifications Boolean @default(false)

  // Email settings
  emailFrequency        String   @default("immediate") // 'immediate', 'daily', 'weekly'

  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
}

model EmailSettings {
  id                String   @id @default(cuid())

  // SMTP Configuration
  smtpHost          String?
  smtpPort          Int?     @default(587)
  smtpSecure        Boolean  @default(false)
  smtpUser          String?
  smtpPassword      String?

  // Email Configuration
  fromEmail         String?
  fromName          String?

  // System settings
  isEnabled         Boolean  @default(false)
  testEmailSent     DateTime?

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("email_settings")
}
