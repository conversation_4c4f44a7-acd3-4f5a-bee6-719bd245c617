"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Area, 
  AreaChart, 
  Bar, 
  BarChart, 
  Line, 
  LineChart, 
  Pie, 
  PieChart, 
  Cell, 
  CartesianGrid, 
  XAxis, 
  YAxis, 
  ResponsiveContainer,
  RadialBarChart,
  RadialBar,
  Legend
} from "recharts";
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import { Brain, TrendingUp, BarChart3, <PERSON><PERSON>hart as PieChartIcon, Activity } from 'lucide-react';

// Sample data for different chart types
const progressData = [
  { month: "يناير", "البعد الاجتماعي": 65, "البعد المعرفي": 72, "البعد الحركي": 58, "البعد التواصلي": 68 },
  { month: "فبراير", "البعد الاجتماعي": 70, "البعد المعرفي": 75, "البعد الحركي": 62, "البعد التواصلي": 71 },
  { month: "مارس", "البعد الاجتماعي": 75, "البعد المعرفي": 78, "البعد الحركي": 67, "البعد التواصلي": 74 },
  { month: "أبريل", "البعد الاجتماعي": 78, "البعد المعرفي": 82, "البعد الحركي": 70, "البعد التواصلي": 77 },
  { month: "مايو", "البعد الاجتماعي": 82, "البعد المعرفي": 85, "البعد الحركي": 73, "البعد التواصلي": 80 },
  { month: "يونيو", "البعد الاجتماعي": 85, "البعد المعرفي": 88, "البعد الحركي": 76, "البعد التواصلي": 83 }
];

const assessmentData = [
  { dimension: "الاجتماعي", achieved: 45, needsDevelopment: 15, total: 60 },
  { dimension: "المعرفي", achieved: 38, needsDevelopment: 22, total: 60 },
  { dimension: "الحركي", achieved: 42, needsDevelopment: 18, total: 60 },
  { dimension: "التواصلي", achieved: 40, needsDevelopment: 20, total: 60 },
  { dimension: "الرعاية الذاتية", achieved: 35, needsDevelopment: 25, total: 60 }
];

const pieData = [
  { name: "متقن", value: 200, fill: "hsl(var(--chart-1))" },
  { name: "يحتاج تطوير", value: 100, fill: "hsl(var(--chart-2))" },
  { name: "غير واضح", value: 50, fill: "hsl(var(--chart-3))" }
];

const radialData = [
  { name: "البعد الاجتماعي", value: 85, fill: "hsl(var(--chart-1))" },
  { name: "البعد المعرفي", value: 78, fill: "hsl(var(--chart-2))" },
  { name: "البعد الحركي", value: 72, fill: "hsl(var(--chart-3))" },
  { name: "البعد التواصلي", value: 80, fill: "hsl(var(--chart-4))" }
];

const chartConfig = {
  "البعد الاجتماعي": {
    label: "البعد الاجتماعي",
    color: "hsl(var(--chart-1))",
  },
  "البعد المعرفي": {
    label: "البعد المعرفي", 
    color: "hsl(var(--chart-2))",
  },
  "البعد الحركي": {
    label: "البعد الحركي",
    color: "hsl(var(--chart-3))",
  },
  "البعد التواصلي": {
    label: "البعد التواصلي",
    color: "hsl(var(--chart-4))",
  },
  achieved: {
    label: "متقن",
    color: "hsl(var(--chart-1))",
  },
  needsDevelopment: {
    label: "يحتاج تطوير",
    color: "hsl(var(--chart-2))",
  }
} satisfies ChartConfig;

export default function ChartDemoPage() {
  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold text-primary flex items-center gap-3">
            <Brain className="h-8 w-8" />
            المخططات التفاعلية
          </h1>
          <p className="text-muted-foreground">
            عرض تفاعلي للبيانات والإحصائيات باستخدام مخططات متنوعة
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="text-xs">
            <Activity className="h-3 w-3 mr-1" />
            تحديث مباشر
          </Badge>
          <Select defaultValue="current-month">
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="current-month">الشهر الحالي</SelectItem>
              <SelectItem value="last-3-months">آخر 3 أشهر</SelectItem>
              <SelectItem value="last-6-months">آخر 6 أشهر</SelectItem>
              <SelectItem value="current-year">السنة الحالية</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Chart Tabs */}
      <Tabs defaultValue="progress" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="progress" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            تقدم الأطفال
          </TabsTrigger>
          <TabsTrigger value="assessment" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            نتائج التقييم
          </TabsTrigger>
          <TabsTrigger value="distribution" className="flex items-center gap-2">
            <PieChartIcon className="h-4 w-4" />
            توزيع المهارات
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            الأداء العام
          </TabsTrigger>
        </TabsList>

        {/* Progress Charts */}
        <TabsContent value="progress" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>تطور الأداء الشهري</CardTitle>
                <CardDescription>
                  رسم بياني يوضح تطور أداء الأطفال عبر الأبعاد المختلفة
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ChartContainer config={chartConfig} className="min-h-[300px] w-full">
                  <LineChart data={progressData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                    <XAxis 
                      dataKey="month" 
                      stroke="hsl(var(--muted-foreground))"
                      fontSize={12}
                    />
                    <YAxis 
                      stroke="hsl(var(--muted-foreground))"
                      fontSize={12}
                    />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <ChartLegend content={<ChartLegendContent />} />
                    <Line
                      type="monotone"
                      dataKey="البعد الاجتماعي"
                      stroke="var(--color-البعد الاجتماعي)"
                      strokeWidth={2}
                      dot={{ fill: "var(--color-البعد الاجتماعي)" }}
                    />
                    <Line
                      type="monotone"
                      dataKey="البعد المعرفي"
                      stroke="var(--color-البعد المعرفي)"
                      strokeWidth={2}
                      dot={{ fill: "var(--color-البعد المعرفي)" }}
                    />
                    <Line
                      type="monotone"
                      dataKey="البعد الحركي"
                      stroke="var(--color-البعد الحركي)"
                      strokeWidth={2}
                      dot={{ fill: "var(--color-البعد الحركي)" }}
                    />
                    <Line
                      type="monotone"
                      dataKey="البعد التواصلي"
                      stroke="var(--color-البعد التواصلي)"
                      strokeWidth={2}
                      dot={{ fill: "var(--color-البعد التواصلي)" }}
                    />
                  </LineChart>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>منطقة التطور</CardTitle>
                <CardDescription>
                  رسم المنطقة يوضح نمو المهارات المتراكم
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ChartContainer config={chartConfig} className="min-h-[300px] w-full">
                  <AreaChart data={progressData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                    <XAxis 
                      dataKey="month" 
                      stroke="hsl(var(--muted-foreground))"
                      fontSize={12}
                    />
                    <YAxis 
                      stroke="hsl(var(--muted-foreground))"
                      fontSize={12}
                    />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Area
                      type="monotone"
                      dataKey="البعد الاجتماعي"
                      stackId="1"
                      stroke="var(--color-البعد الاجتماعي)"
                      fill="var(--color-البعد الاجتماعي)"
                      fillOpacity={0.6}
                    />
                    <Area
                      type="monotone"
                      dataKey="البعد المعرفي"
                      stackId="1"
                      stroke="var(--color-البعد المعرفي)"
                      fill="var(--color-البعد المعرفي)"
                      fillOpacity={0.6}
                    />
                  </AreaChart>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Assessment Charts */}
        <TabsContent value="assessment" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>مقارنة أداء الأبعاد</CardTitle>
              <CardDescription>
                مخطط أعمدة يقارن المهارات المتقنة والتي تحتاج تطوير
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer config={chartConfig} className="min-h-[400px] w-full">
                <BarChart data={assessmentData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                  <XAxis 
                    dataKey="dimension" 
                    stroke="hsl(var(--muted-foreground))"
                    fontSize={12}
                    angle={-45}
                    textAnchor="end"
                    height={80}
                  />
                  <YAxis 
                    stroke="hsl(var(--muted-foreground))"
                    fontSize={12}
                  />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <ChartLegend content={<ChartLegendContent />} />
                  <Bar
                    dataKey="achieved"
                    fill="var(--color-achieved)"
                    radius={[4, 4, 0, 0]}
                  />
                  <Bar
                    dataKey="needsDevelopment"
                    fill="var(--color-needsDevelopment)"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ChartContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Distribution Charts */}
        <TabsContent value="distribution" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>توزيع حالة المهارات</CardTitle>
                <CardDescription>
                  مخطط دائري يوضح نسب المهارات المختلفة
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ChartContainer config={chartConfig} className="min-h-[300px] w-full">
                  <PieChart>
                    <Pie
                      data={pieData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      paddingAngle={5}
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {pieData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.fill} />
                      ))}
                    </Pie>
                    <ChartTooltip content={<ChartTooltipContent />} />
                  </PieChart>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>أداء الأبعاد الشعاعي</CardTitle>
                <CardDescription>
                  مخطط شعاعي يوضح نسبة إنجاز كل بُعد
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ChartContainer config={chartConfig} className="min-h-[300px] w-full">
                  <RadialBarChart
                    cx="50%"
                    cy="50%"
                    innerRadius="20%"
                    outerRadius="80%"
                    data={radialData}
                  >
                    <RadialBar
                      dataKey="value"
                      cornerRadius={10}
                      label={{ position: 'insideStart', fill: '#fff' }}
                    />
                    <Legend
                      iconSize={18}
                      layout="vertical"
                      verticalAlign="middle"
                      align="right"
                    />
                    <ChartTooltip content={<ChartTooltipContent />} />
                  </RadialBarChart>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Performance Overview */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  إجمالي المهارات المتقنة
                </CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">200</div>
                <p className="text-xs text-muted-foreground">
                  +12% من الشهر الماضي
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  المهارات قيد التطوير
                </CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">100</div>
                <p className="text-xs text-muted-foreground">
                  -5% من الشهر الماضي
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  متوسط التقدم
                </CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">78%</div>
                <p className="text-xs text-muted-foreground">
                  +8% من الشهر الماضي
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>نظرة عامة على الأداء</CardTitle>
              <CardDescription>
                مخطط شامل يجمع جميع المؤشرات الرئيسية
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer config={chartConfig} className="min-h-[400px] w-full">
                <AreaChart data={progressData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                  <XAxis 
                    dataKey="month" 
                    stroke="hsl(var(--muted-foreground))"
                    fontSize={12}
                  />
                  <YAxis 
                    stroke="hsl(var(--muted-foreground))"
                    fontSize={12}
                  />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <ChartLegend content={<ChartLegendContent />} />
                  <Area
                    type="monotone"
                    dataKey="البعد الاجتماعي"
                    stackId="1"
                    stroke="var(--color-البعد الاجتماعي)"
                    fill="var(--color-البعد الاجتماعي)"
                    fillOpacity={0.8}
                  />
                  <Area
                    type="monotone"
                    dataKey="البعد المعرفي"
                    stackId="1"
                    stroke="var(--color-البعد المعرفي)"
                    fill="var(--color-البعد المعرفي)"
                    fillOpacity={0.8}
                  />
                  <Area
                    type="monotone"
                    dataKey="البعد الحركي"
                    stackId="1"
                    stroke="var(--color-البعد الحركي)"
                    fill="var(--color-البعد الحركي)"
                    fillOpacity={0.8}
                  />
                  <Area
                    type="monotone"
                    dataKey="البعد التواصلي"
                    stackId="1"
                    stroke="var(--color-البعد التواصلي)"
                    fill="var(--color-البعد التواصلي)"
                    fillOpacity={0.8}
                  />
                </AreaChart>
                </ChartContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <div className="flex justify-center gap-4 pt-6">
        <Button variant="outline">
          تصدير البيانات
        </Button>
        <Button>
          إنشاء تقرير مفصل
        </Button>
      </div>
    </div>
  );
} 