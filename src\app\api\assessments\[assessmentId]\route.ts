import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

export async function GET(
  request: Request,
  { params }: { params: { assessmentId: string } }
) {
  const assessment = await prisma.assessment.findUnique({
    where: { id: params.assessmentId },
  });
  return NextResponse.json(assessment);
}

export async function PUT(
  request: Request,
  { params }: { params: { assessmentId: string } }
) {
  const data = await request.json();
  const assessment = await prisma.assessment.update({
    where: { id: params.assessmentId },
    data,
  });
  return NextResponse.json(assessment);
}

export async function DELETE(
  request: Request,
  { params }: { params: { assessmentId: string } }
) {
  await prisma.assessment.delete({
    where: { id: params.assessmentId },
  });
  return new Response(null, { status: 204 });
}
