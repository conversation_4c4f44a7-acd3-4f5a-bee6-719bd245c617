import type { Child, Assessment, User, LearningPlan, ComprehensiveReport, PlanNote, CaseStudyData } from './types';

const API_BASE_URL = '/api';

async function fetchFromAPI<T>(url: string, options?: RequestInit): Promise<T> {
  const response = await fetch(url, options);
  if (!response.ok) {
    throw new Error(`Failed to fetch from ${url}`);
  }
  return response.json();
}

// Children storage functions
export async function getChildren(): Promise<Child[]> {
  return fetchFromAPI(`${API_BASE_URL}/children`);
}

export async function getChildById(id: string): Promise<Child | null> {
  return fetchFromAPI(`${API_BASE_URL}/children/${id}`);
}

export async function saveChild(child: Partial<Child>): Promise<Child> {
  const url = child.id ? `${API_BASE_URL}/children/${child.id}` : `${API_BASE_URL}/children`;
  const method = child.id ? 'PUT' : 'POST';
  return fetchFromAPI(url, {
    method,
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(child),
  });
}

export async function deleteChild(childId: string): Promise<void> {
  await fetchFromAPI(`${API_BASE_URL}/children/${childId}`, { method: 'DELETE' });
}

// Assessment storage functions
export async function getAssessments(): Promise<Assessment[]> {
  return fetchFromAPI(`${API_BASE_URL}/assessments`);
}

export async function getAssessmentsByChildId(childId: string): Promise<Assessment[]> {
  // This will require a custom endpoint or filtering on the client
  const allAssessments = await getAssessments();
  return allAssessments.filter(assessment => assessment.childId === childId);
}

export async function getAssessmentById(id: string): Promise<Assessment | null> {
  return fetchFromAPI(`${API_BASE_URL}/assessments/${id}`);
}

export async function saveAssessment(assessment: Partial<Assessment>): Promise<Assessment> {
  const url = assessment.id ? `${API_BASE_URL}/assessments/${assessment.id}` : `${API_BASE_URL}/assessments`;
  const method = assessment.id ? 'PUT' : 'POST';
  return fetchFromAPI(url, {
    method,
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(assessment),
  });
}

export async function deleteAssessment(assessmentId: string): Promise<void> {
  await fetchFromAPI(`${API_BASE_URL}/assessments/${assessmentId}`, { method: 'DELETE' });
}

// User storage functions
export async function getUsers(): Promise<User[]> {
  return fetchFromAPI(`${API_BASE_URL}/users`);
}

export async function getUserById(id: string): Promise<User | null> {
  return fetchFromAPI(`${API_BASE_URL}/users/${id}`);
}

export async function saveUser(user: Partial<User>): Promise<User> {
  const url = user.id ? `${API_BASE_URL}/users/${user.id}` : `${API_BASE_URL}/users`;
  const method = user.id ? 'PUT' : 'POST';
  return fetchFromAPI(url, {
    method,
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(user),
  });
}

// Learning Plans storage functions
export async function getLearningPlans(): Promise<LearningPlan[]> {
  return fetchFromAPI(`${API_BASE_URL}/learning-plans`);
}

export async function getLearningPlansByChildId(childId: string): Promise<LearningPlan[]> {
  const allPlans = await getLearningPlans();
  return allPlans.filter(plan => plan.childId === childId);
}

export async function saveLearningPlan(plan: Partial<LearningPlan>): Promise<LearningPlan> {
  const url = plan.id ? `${API_BASE_URL}/learning-plans/${plan.id}` : `${API_BASE_URL}/learning-plans`;
  const method = plan.id ? 'PUT' : 'POST';
  return fetchFromAPI(url, {
    method,
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(plan),
  });
}

// Comprehensive Reports storage functions
export async function getComprehensiveReports(): Promise<ComprehensiveReport[]> {
  return fetchFromAPI(`${API_BASE_URL}/comprehensive-reports`);
}

export async function getComprehensiveReportsByChildId(childId: string): Promise<ComprehensiveReport[]> {
  const allReports = await getComprehensiveReports();
  return allReports.filter(report => report.childId === childId);
}

export async function saveComprehensiveReport(report: Partial<ComprehensiveReport>): Promise<ComprehensiveReport> {
  const url = report.id ? `${API_BASE_URL}/comprehensive-reports/${report.id}` : `${API_BASE_URL}/comprehensive-reports`;
  const method = report.id ? 'PUT' : 'POST';
  return fetchFromAPI(url, {
    method,
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(report),
  });
}

export async function getComprehensiveReportById(reportId: string): Promise<ComprehensiveReport | null> {
  return fetchFromAPI(`${API_BASE_URL}/comprehensive-reports/${reportId}`);
}

export async function deleteComprehensiveReport(reportId: string): Promise<void> {
  await fetchFromAPI(`${API_BASE_URL}/comprehensive-reports/${reportId}`, { method: 'DELETE' });
}

// Plan Notes storage functions
export async function getPlanNotes(): Promise<PlanNote[]> {
    return fetchFromAPI(`${API_BASE_URL}/plan-notes`);
}

export async function getPlanNotesByChildId(childId: string): Promise<PlanNote[]> {
    const allNotes = await getPlanNotes();
    return allNotes.filter(note => note.childId === childId);
}

export async function savePlanNote(note: Partial<PlanNote>): Promise<PlanNote> {
    const url = note.id ? `${API_BASE_URL}/plan-notes/${note.id}` : `${API_BASE_URL}/plan-notes`;
    const method = note.id ? 'PUT' : 'POST';
    return fetchFromAPI(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(note),
    });
}

// CaseStudyData storage functions
export async function getCaseStudyData(childId: string): Promise<CaseStudyData | null> {
    const child = await getChildById(childId);
    if (!child) {
        return null;
    }
    return child.caseStudy || null;
}

export async function saveCaseStudyData(childId: string, caseStudy: CaseStudyData): Promise<Child> {
    const child = await getChildById(childId);
    if (!child) {
        throw new Error('Child not found');
    }
    const updatedChild = { ...child, caseStudy };
    return saveChild(updatedChild);
}
