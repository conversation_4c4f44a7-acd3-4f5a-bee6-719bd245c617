"use client";

import type { ChartConfig } from "@/components/ui/chart";
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";

interface AgeDistributionDataPoint {
  label: string;
  minMonths: number;
  maxMonths: number;
  count: number;
  fill: string;
}

interface AgeDistributionChartProps {
  data: AgeDistributionDataPoint[];
  config: ChartConfig;
}

export default function AgeDistributionChart({ data, config }: AgeDistributionChartProps) {
  if (!data || data.length === 0) {
    return <p className="text-muted-foreground text-center py-4">لا توجد بيانات كافية لعرض توزيع الأعمار.</p>;
  }

  return (
    <div className="h-[300px] w-full">
      <ChartContainer config={config} className="w-full h-full">
        <AreaChart 
          data={data} 
          margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
          <XAxis 
            dataKey="label" 
            stroke="hsl(var(--muted-foreground))" 
            fontSize={12}
            angle={-45}
            textAnchor="end"
            height={60}
          />
          <YAxis 
            stroke="hsl(var(--muted-foreground))" 
            fontSize={12}
            allowDecimals={false}
          />
          <ChartTooltip
            content={<ChartTooltipContent />}
          />
          <Area
            type="monotone"
            dataKey="count"
            stroke="hsl(var(--chart-1))"
            fill="hsl(var(--chart-1))"
            fillOpacity={0.6}
            strokeWidth={3}
          />
        </AreaChart>
      </ChartContainer>
    </div>
  );
}
