import Link from 'next/link';
import type { Child } from '@/lib/types';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CalendarDays, UserSquare, Edit, Trash2, Briefcase, Hash } from 'lucide-react';
import AgeDisplay from '@/components/assessment/AgeDisplay';
import { formatDate } from '@/lib/utils';

interface ChildCardProps {
  child: Child;
  onEdit: (child: Child) => void;
  onDelete: (childId: string) => void;
}

export default function ChildCard({ child, onEdit, onDelete }: ChildCardProps) {
  const fallbackName = child.name.split(' ').map(n => n[0]).join('').toUpperCase() || 'C';

  return (
    <Card className="flex flex-col overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
      <CardHeader className="flex flex-row-reverse items-center gap-4 p-4 bg-muted/30">
        <Avatar className="h-20 w-20 border-2 border-primary shadow-md">
          <AvatarImage src={child.avatarUrl} alt={child.name} className="object-cover"/>
          <AvatarFallback className="text-xl font-semibold bg-gradient-to-br from-primary/20 to-primary/10">
            {fallbackName}
          </AvatarFallback>
        </Avatar>
        <div className="flex-1 text-right">
          <div className="flex items-center justify-end gap-2 mb-1">
            <Badge variant="outline" className="text-xs font-mono">
              {child.childIdNumber}
              <Hash className="h-3 w-3 ml-1" />
            </Badge>
          </div>
          <CardTitle className="text-xl text-primary transition-colors">
            <Link href={`/children/${child.id}`} className="hover:text-primary/80">
              {child.name}
            </Link>
          </CardTitle>
          <AgeDisplay 
            birthDate={child.birthDate} 
            className="text-sm text-muted-foreground text-right" 
            label="العمر:" 
          />
        </div>
      </CardHeader>
      
      <CardContent className="p-4 flex-grow">
        <div className="space-y-3 text-sm">
          <div className="flex items-center gap-2 justify-end">
            <span className="text-right">{formatDate(child.birthDate, 'PPP')} :تاريخ الميلاد</span>
            <CalendarDays className="h-4 w-4 text-accent" />
          </div>
          <div className="flex items-center gap-2 justify-end">
            <span className="text-right">{formatDate(child.enrollmentDate, 'PPP')} :تاريخ الالتحاق</span>
            <Briefcase className="h-4 w-4 text-accent" />
          </div>
          <div className="flex items-center gap-2 justify-end">
            <span className="text-right">{child.specialistName} :الأخصائي</span>
            <UserSquare className="h-4 w-4 text-accent" />
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="p-4 border-t flex flex-col sm:flex-row-reverse gap-2">
        <div className="flex gap-2 w-full sm:w-auto">
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={() => onDelete(child.id)} 
            className="text-red-500 hover:text-red-700 hover:bg-red-50 flex-1 sm:flex-none"
          >
            <Trash2 className="h-4 w-4" />
            <span className="sr-only">حذف</span>
          </Button>
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={() => onEdit(child)} 
            className="text-blue-500 hover:text-blue-700 hover:bg-blue-50 flex-1 sm:flex-none"
          >
            <Edit className="h-4 w-4" />
            <span className="sr-only">تعديل</span>
          </Button>
        </div>
        <Link href={`/children/${child.id}`} className="w-full sm:w-auto flex-grow">
          <Button variant="outline" className="w-full">
            عرض الملف الشخصي
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
