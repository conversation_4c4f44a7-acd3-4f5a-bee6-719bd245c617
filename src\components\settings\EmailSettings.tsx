"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Mail, Send, CheckCircle, AlertCircle } from 'lucide-react';
import { useEmailSettings } from '@/hooks/useEmailSettings';
import { useToast } from '@/hooks/use-toast';
import { useSession } from 'next-auth/react';

export function EmailSettings() {
  const { data: session } = useSession();
  const { settings, isLoading, error, updateSettings, sendTestEmail } = useEmailSettings();
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    smtpHost: '',
    smtpPort: 587,
    smtpSecure: false,
    smtpUser: '',
    smtpPassword: '',
    fromEmail: '',
    fromName: '',
    isEnabled: false,
  });
  const [testEmail, setTestEmail] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [isSendingTest, setIsSendingTest] = useState(false);

  // Update form data when settings are loaded
  React.useEffect(() => {
    if (settings) {
      setFormData({
        smtpHost: settings.smtpHost || '',
        smtpPort: settings.smtpPort || 587,
        smtpSecure: settings.smtpSecure || false,
        smtpUser: settings.smtpUser || '',
        smtpPassword: '', // Don't populate password for security
        fromEmail: settings.fromEmail || '',
        fromName: settings.fromName || '',
        isEnabled: settings.isEnabled || false,
      });
    }
  }, [settings]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Remove empty password to avoid overwriting existing password
      const dataToSave = { ...formData };
      if (!dataToSave.smtpPassword) {
        delete dataToSave.smtpPassword;
      }

      await updateSettings(dataToSave);
      toast({
        title: "تم الحفظ",
        description: "تم حفظ إعدادات البريد الإلكتروني بنجاح.",
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في حفظ إعدادات البريد الإلكتروني.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleTestEmail = async () => {
    setIsSendingTest(true);
    try {
      await sendTestEmail(testEmail || undefined);
      toast({
        title: "تم الإرسال",
        description: "تم إرسال البريد الاختباري بنجاح.",
      });
      setTestEmail('');
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في إرسال البريد الاختباري. تحقق من الإعدادات.",
        variant: "destructive",
      });
    } finally {
      setIsSendingTest(false);
    }
  };

  // Check if user is admin before attempting to load settings
  const isAdmin = session?.user?.role === 'admin';

  // Don't render for non-admin users
  if (!isAdmin) {
    return (
      <Card>
        <CardContent className="p-6">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              يتطلب الوصول إلى إعدادات البريد الإلكتروني صلاحيات المدير.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (isLoading && !settings) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <Loader2 className="h-6 w-6 animate-spin ml-2" />
          <span>جاري تحميل إعدادات البريد الإلكتروني...</span>
        </CardContent>
      </Card>
    );
  }

  if (error && error.includes('Admin')) {
    return (
      <Card>
        <CardContent className="p-6">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              يتطلب الوصول إلى إعدادات البريد الإلكتروني صلاحيات المدير.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Mail className="h-5 w-5" />
          <CardTitle>إعدادات البريد الإلكتروني</CardTitle>
        </div>
        <CardDescription>
          تكوين خادم SMTP لإرسال الإشعارات عبر البريد الإلكتروني
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {error && !error.includes('Admin') && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Enable Email Service */}
        <div className="flex items-center justify-between space-x-2 space-x-reverse border p-4 rounded-lg">
          <Label htmlFor="email-enabled" className="flex flex-col space-y-1 cursor-pointer">
            <span>تفعيل خدمة البريد الإلكتروني</span>
            <span className="font-normal leading-snug text-muted-foreground">
              تمكين إرسال الإشعارات عبر البريد الإلكتروني
            </span>
          </Label>
          <Switch
            id="email-enabled"
            checked={formData.isEnabled}
            onCheckedChange={(checked) => handleInputChange('isEnabled', checked)}
          />
        </div>

        {/* SMTP Configuration */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="smtp-host">خادم SMTP</Label>
            <Input
              id="smtp-host"
              value={formData.smtpHost}
              onChange={(e) => handleInputChange('smtpHost', e.target.value)}
              placeholder="smtp.gmail.com"
              className="mt-1"
            />
          </div>
          <div>
            <Label htmlFor="smtp-port">منفذ SMTP</Label>
            <Input
              id="smtp-port"
              type="number"
              value={formData.smtpPort}
              onChange={(e) => handleInputChange('smtpPort', parseInt(e.target.value) || 587)}
              className="mt-1"
            />
          </div>
        </div>

        <div className="flex items-center justify-between space-x-2 space-x-reverse border p-4 rounded-lg">
          <Label htmlFor="smtp-secure" className="flex flex-col space-y-1 cursor-pointer">
            <span>اتصال آمن (SSL/TLS)</span>
            <span className="font-normal leading-snug text-muted-foreground">
              استخدام اتصال مشفر للأمان
            </span>
          </Label>
          <Switch
            id="smtp-secure"
            checked={formData.smtpSecure}
            onCheckedChange={(checked) => handleInputChange('smtpSecure', checked)}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="smtp-user">اسم المستخدم</Label>
            <Input
              id="smtp-user"
              value={formData.smtpUser}
              onChange={(e) => handleInputChange('smtpUser', e.target.value)}
              placeholder="<EMAIL>"
              className="mt-1"
            />
          </div>
          <div>
            <Label htmlFor="smtp-password">كلمة المرور</Label>
            <Input
              id="smtp-password"
              type="password"
              value={formData.smtpPassword}
              onChange={(e) => handleInputChange('smtpPassword', e.target.value)}
              placeholder={settings?.smtpPassword ? "••••••••" : "كلمة المرور"}
              className="mt-1"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="from-email">البريد الإلكتروني للمرسل</Label>
            <Input
              id="from-email"
              type="email"
              value={formData.fromEmail}
              onChange={(e) => handleInputChange('fromEmail', e.target.value)}
              placeholder="<EMAIL>"
              className="mt-1"
            />
          </div>
          <div>
            <Label htmlFor="from-name">اسم المرسل</Label>
            <Input
              id="from-name"
              value={formData.fromName}
              onChange={(e) => handleInputChange('fromName', e.target.value)}
              placeholder="نظام البرنامج"
              className="mt-1"
            />
          </div>
        </div>

        {/* Test Email Section */}
        <div className="border-t pt-6">
          <h4 className="text-sm font-medium mb-3">اختبار البريد الإلكتروني</h4>
          <div className="flex gap-2">
            <Input
              value={testEmail}
              onChange={(e) => setTestEmail(e.target.value)}
              placeholder="البريد الإلكتروني للاختبار (اختياري)"
              type="email"
              className="flex-1"
            />
            <Button
              onClick={handleTestEmail}
              disabled={isSendingTest || !formData.isEnabled}
              variant="outline"
            >
              {isSendingTest ? (
                <Loader2 className="h-4 w-4 animate-spin ml-1" />
              ) : (
                <Send className="h-4 w-4 ml-1" />
              )}
              إرسال اختبار
            </Button>
          </div>
          {settings?.testEmailSent && (
            <p className="text-xs text-muted-foreground mt-2">
              آخر اختبار: {new Date(settings.testEmailSent).toLocaleString('ar-SA')}
            </p>
          )}
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? (
              <Loader2 className="h-4 w-4 animate-spin ml-1" />
            ) : (
              <CheckCircle className="h-4 w-4 ml-1" />
            )}
            حفظ الإعدادات
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
