import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for comprehensive report data
const comprehensiveReportSchema = z.object({
  childId: z.string().min(1, 'Child ID is required'),
  assessmentId: z.string().min(1, 'Assessment ID is required'),
  childName: z.string().min(1, 'Child name is required'),
  assessmentDate: z.string().datetime('Invalid assessment date'),
  childAgeInMonths: z.number().min(0, 'Child age must be positive'),
  additionalFocus: z.string().optional(),
  executiveSummary: z.string().min(1, 'Executive summary is required'),
  strengths: z.array(z.string()).min(1, 'At least one strength is required'),
  areasForDevelopment: z.array(z.string()).min(1, 'At least one area for development is required'),
  dataAnalysisHighlights: z.array(z.string()).min(1, 'At least one data analysis highlight is required'),
  actionableRecommendations: z.array(z.string()).min(1, 'At least one recommendation is required'),
});

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const childId = searchParams.get('childId');

    let whereClause = {};
    if (childId) {
      whereClause = { childId };
    }

    const comprehensiveReports = await prisma.comprehensiveReport.findMany({
      where: whereClause,
      include: {
        child: {
          select: {
            id: true,
            name: true,
            childIdNumber: true,
          },
        },
        assessment: {
          select: {
            id: true,
            assessmentDate: true,
          },
        },
      },
      orderBy: { generatedDate: 'desc' },
    });

    // Parse JSON fields back to arrays for all reports
    const reportsWithParsedArrays = comprehensiveReports.map(report => ({
      ...report,
      strengths: JSON.parse(report.strengths),
      areasForDevelopment: JSON.parse(report.areasForDevelopment),
      dataAnalysisHighlights: JSON.parse(report.dataAnalysisHighlights),
      actionableRecommendations: JSON.parse(report.actionableRecommendations),
    }));

    return NextResponse.json(reportsWithParsedArrays);
  } catch (error) {
    console.error('Error fetching comprehensive reports:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only certain roles can create comprehensive reports
    const allowedRoles = ['super_admin', 'eiu_manager', 'case_manager', 'specialist'];
    if (!allowedRoles.includes(session.user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = comprehensiveReportSchema.parse(body);

    // Verify child and assessment exist
    const child = await prisma.child.findUnique({
      where: { id: validatedData.childId },
    });

    if (!child) {
      return NextResponse.json(
        { error: 'Child not found' },
        { status: 404 }
      );
    }

    const assessment = await prisma.assessment.findUnique({
      where: { id: validatedData.assessmentId },
    });

    if (!assessment) {
      return NextResponse.json(
        { error: 'Assessment not found' },
        { status: 404 }
      );
    }

    const comprehensiveReport = await prisma.comprehensiveReport.create({
      data: {
        childId: validatedData.childId,
        assessmentId: validatedData.assessmentId,
        childName: validatedData.childName,
        assessmentDate: new Date(validatedData.assessmentDate),
        childAgeInMonths: validatedData.childAgeInMonths,
        additionalFocus: validatedData.additionalFocus,
        executiveSummary: validatedData.executiveSummary,
        strengths: JSON.stringify(validatedData.strengths),
        areasForDevelopment: JSON.stringify(validatedData.areasForDevelopment),
        dataAnalysisHighlights: JSON.stringify(validatedData.dataAnalysisHighlights),
        actionableRecommendations: JSON.stringify(validatedData.actionableRecommendations),
        generatedDate: new Date(),
      },
      include: {
        child: {
          select: {
            id: true,
            name: true,
            childIdNumber: true,
          },
        },
        assessment: {
          select: {
            id: true,
            assessmentDate: true,
          },
        },
      },
    });

    return NextResponse.json(comprehensiveReport, { status: 201 });
  } catch (error) {
    console.error('Error creating comprehensive report:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
