import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

export async function GET() {
  const comprehensiveReports = await prisma.comprehensiveReport.findMany();
  return NextResponse.json(comprehensiveReports);
}

export async function POST(request: Request) {
  const data = await request.json();
  const comprehensiveReport = await prisma.comprehensiveReport.create({ data });
  return NextResponse.json(comprehensiveReport, { status: 201 });
}
