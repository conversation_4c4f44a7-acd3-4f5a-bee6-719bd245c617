import { toast as sonnerToast } from "sonner"
import { CheckCircle, XCircle, AlertTriangle, Info, Loader2 } from "lucide-react"

type ToastType = "success" | "error" | "warning" | "info" | "loading"

interface EnhancedToastOptions {
  title?: string
  description?: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
  cancel?: {
    label: string
    onClick?: () => void
  }
}

const getToastIcon = (type: ToastType) => {
  switch (type) {
    case "success":
      return <CheckCircle className="h-4 w-4 text-green-500" />
    case "error":
      return <XCircle className="h-4 w-4 text-red-500" />
    case "warning":
      return <AlertTriangle className="h-4 w-4 text-yellow-500" />
    case "info":
      return <Info className="h-4 w-4 text-blue-500" />
    case "loading":
      return <Loader2 className="h-4 w-4 animate-spin text-gray-500" />
    default:
      return null
  }
}

export const enhancedToast = {
  success: (message: string, options?: EnhancedToastOptions) => {
    return sonnerToast.success(message, {
      description: options?.description,
      duration: options?.duration || 4000,
      icon: getToastIcon("success"),
      action: options?.action,
      cancel: options?.cancel,
      style: {
        direction: "rtl",
        textAlign: "right",
      },
    })
  },

  error: (message: string, options?: EnhancedToastOptions) => {
    return sonnerToast.error(message, {
      description: options?.description,
      duration: options?.duration || 6000,
      icon: getToastIcon("error"),
      action: options?.action,
      cancel: options?.cancel,
      style: {
        direction: "rtl",
        textAlign: "right",
      },
    })
  },

  warning: (message: string, options?: EnhancedToastOptions) => {
    return sonnerToast.warning(message, {
      description: options?.description,
      duration: options?.duration || 5000,
      icon: getToastIcon("warning"),
      action: options?.action,
      cancel: options?.cancel,
      style: {
        direction: "rtl",
        textAlign: "right",
      },
    })
  },

  info: (message: string, options?: EnhancedToastOptions) => {
    return sonnerToast.info(message, {
      description: options?.description,
      duration: options?.duration || 4000,
      icon: getToastIcon("info"),
      action: options?.action,
      cancel: options?.cancel,
      style: {
        direction: "rtl",
        textAlign: "right",
      },
    })
  },

  loading: (message: string, options?: EnhancedToastOptions) => {
    return sonnerToast.loading(message, {
      description: options?.description,
      icon: getToastIcon("loading"),
      style: {
        direction: "rtl",
        textAlign: "right",
      },
    })
  },

  promise: <T,>(
    promise: Promise<T>,
    {
      loading,
      success,
      error,
    }: {
      loading: string
      success: string | ((data: T) => string)
      error: string | ((error: any) => string)
    }
  ) => {
    return sonnerToast.promise(promise, {
      loading,
      success,
      error,
      style: {
        direction: "rtl",
        textAlign: "right",
      },
    })
  },

  dismiss: (toastId?: string | number) => {
    sonnerToast.dismiss(toastId)
  },
}

export type { EnhancedToastOptions } 