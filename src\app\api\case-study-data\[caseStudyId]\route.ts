import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

export async function GET(
  request: Request,
  { params }: { params: { caseStudyId: string } }
) {
  const caseStudyData = await prisma.caseStudyData.findUnique({
    where: { id: params.caseStudyId },
  });
  return NextResponse.json(caseStudyData);
}

export async function PUT(
  request: Request,
  { params }: { params: { caseStudyId: string } }
) {
  const data = await request.json();
  const caseStudyData = await prisma.caseStudyData.update({
    where: { id: params.caseStudyId },
    data,
  });
  return NextResponse.json(caseStudyData);
}

export async function DELETE(
  request: Request,
  { params }: { params: { caseStudyId: string } }
) {
  await prisma.caseStudyData.delete({
    where: { id: params.caseStudyId },
  });
  return new Response(null, { status: 204 });
}
