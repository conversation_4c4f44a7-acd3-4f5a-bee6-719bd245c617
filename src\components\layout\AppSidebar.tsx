"use client";

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  Home, 
  Users, 
  ClipboardList, 
  Lightbulb, 
  BarChart3, 
  Settings, 
  UserCog, 
  Database,
  Brain,
  GraduationCap,
  FileText,
  Calendar,
  TrendingUp,
  Shield
} from 'lucide-react'; 
import { cn } from '@/lib/utils';
import {
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarMenuSubButton,
} from '@/components/ui/sidebar';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface NavSubItem {
  href: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
}

interface NavItem {
  href: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  subItems?: NavSubItem[];
  badge?: string;
}

interface NavSection {
  category: string;
  items: NavItem[];
}

const navItems: NavSection[] = [
  {
    category: 'الرئيسية',
    items: [
      { 
        href: '/', 
        label: 'الصفحة الرئيسية', 
        icon: Home,
        description: 'نظرة عامة على النشاطات والإحصائيات'
      },
    ]
  },
  {
    category: 'إدارة الأطفال', 
    items: [
      { 
        href: '/children', 
        label: 'الأطفال', 
        icon: Users,
        description: 'إدارة بيانات الأطفال'
      },
      { 
        href: '/assessment', 
        label: 'التقييمات', 
        icon: ClipboardList,
        description: 'إجراء وإدارة التقييمات'
      },
      { 
        href: '/learning-plan', 
        label: 'الخطط التعليمية', 
        icon: GraduationCap,
        description: 'خطط التطوير والتعلم'
      },
    ]
  },
  {
    category: 'التقارير والإحصائيات',
    items: [
      { 
        href: '/reports', 
        label: 'التقارير', 
        icon: BarChart3,
        description: 'تقارير مفصلة وإحصائيات',
        subItems: [
          { href: '/reports/progress', label: 'تقارير التقدم', icon: TrendingUp },
          { href: '/reports/assessment', label: 'تقارير التقييم', icon: ClipboardList },
          { href: '/reports/summary', label: 'ملخص الأداء', icon: FileText },
        ]
      },
      { 
        href: '/chart-demo', 
        label: 'المخططات التفاعلية', 
        icon: Brain,
        description: 'عرض بياني للبيانات'
      },
    ]
  },
  {
    category: 'الإدارة والإعدادات',
    items: [
      {
        href: '/users',
        label: 'إدارة المستخدمين',
        icon: Shield,
        description: 'إدارة المستخدمين والصلاحيات'
      },
      {
        href: '/settings',
        label: 'الإعدادات',
        icon: Settings,
        description: 'إعدادات النظام والتطبيق'
      },
    ]
  }
];

export default function AppSidebar() {
  const pathname = usePathname();

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex-1 overflow-y-auto overscroll-contain">
        <div className="space-y-6 py-4">
          {navItems.map((section, sectionIndex) => (
            <SidebarGroup key={sectionIndex} className="px-3">
              <SidebarGroupLabel className="text-sidebar-foreground/70 font-bold text-sm mb-3 flex items-center gap-3 px-2">
                <div className="w-1 h-5 bg-primary rounded-full flex-shrink-0" />
                <span className="flex-1 text-right leading-relaxed">{section.category}</span>
              </SidebarGroupLabel>
              
              <SidebarGroupContent>
                <SidebarMenu className="space-y-1">
                  {section.items.map((item, itemIndex) => {
                    const isActive = pathname === item.href || (item.href !== "/" && pathname.startsWith(item.href));
                    const hasSubItems = item.subItems && item.subItems.length > 0;
                    
                    if (hasSubItems) {
                      return (
                        <Collapsible key={item.href} defaultOpen={isActive}>
                          <SidebarMenuItem>
                            <CollapsibleTrigger asChild>
                              <SidebarMenuButton
                                className={cn(
                                  'w-full h-11 px-3 py-2.5 rounded-lg transition-all duration-200 group',
                                  'hover:bg-sidebar-accent/80 hover:shadow-sm',
                                  isActive
                                    ? 'bg-sidebar-accent text-sidebar-accent-foreground font-semibold shadow-sm border border-sidebar-border/40'
                                    : 'text-sidebar-foreground/90 hover:text-sidebar-foreground'
                                )}
                                tooltip={{
                                  children: item.description,
                                  side: "left",
                                  align: "center"
                                }}
                              >
                                <div className="flex items-center gap-3 w-full">
                                  <span className="group-data-[collapsible=icon]:hidden text-sm font-medium flex-1 text-right leading-relaxed">
                                    {item.label}
                                  </span>
                                  {item.badge && (
                                    <Badge variant="secondary" className="text-xs px-2 py-0.5 font-medium">
                                      {item.badge}
                                    </Badge>
                                  )}
                                  <item.icon className="h-4 w-4 flex-shrink-0 opacity-80" />
                                </div>
                              </SidebarMenuButton>
                            </CollapsibleTrigger>
                            <CollapsibleContent>
                              <SidebarMenuSub className="mt-2 mr-6 space-y-1">
                                {item.subItems?.map((subItem, subIndex) => {
                                  const isSubActive = pathname === subItem.href;
                                  return (
                                    <SidebarMenuSubItem key={subItem.href}>
                                      <SidebarMenuSubButton
                                        asChild
                                        className={cn(
                                          'h-9 px-3 py-2 rounded-md transition-all duration-200',
                                          'hover:bg-sidebar-accent/60 hover:shadow-sm',
                                          isSubActive
                                            ? 'bg-sidebar-accent/80 text-sidebar-accent-foreground font-medium shadow-sm'
                                            : 'text-sidebar-foreground/75 hover:text-sidebar-foreground'
                                        )}
                                        isActive={isSubActive}
                                      >
                                        <Link href={subItem.href} className="flex items-center gap-3 w-full">
                                          <span className="text-sm flex-1 text-right leading-relaxed">{subItem.label}</span>
                                          <subItem.icon className="h-3.5 w-3.5 flex-shrink-0 opacity-70" />
                                        </Link>
                                      </SidebarMenuSubButton>
                                    </SidebarMenuSubItem>
                                  );
                                })}
                              </SidebarMenuSub>
                            </CollapsibleContent>
                          </SidebarMenuItem>
                        </Collapsible>
                      );
                    }

                    return (
                      <SidebarMenuItem key={item.href}>
                        <SidebarMenuButton
                          asChild
                          className={cn(
                            'w-full h-11 px-3 py-2.5 rounded-lg transition-all duration-200 group',
                            'hover:bg-sidebar-accent/80 hover:shadow-sm',
                            isActive
                              ? 'bg-sidebar-accent text-sidebar-accent-foreground font-semibold shadow-sm border border-sidebar-border/40'
                              : 'text-sidebar-foreground/90 hover:text-sidebar-foreground'
                          )}
                          isActive={isActive}
                          tooltip={{
                            children: item.description,
                            side: "left",
                            align: "center"
                          }}
                        >
                          <Link href={item.href} className="flex items-center gap-3 w-full">
                            <span className="group-data-[collapsible=icon]:hidden text-sm font-medium flex-1 text-right leading-relaxed">
                              {item.label}
                            </span>
                            {item.badge && (
                              <Badge variant="secondary" className="text-xs px-2 py-0.5 font-medium">
                                {item.badge}
                              </Badge>
                            )}
                            <item.icon className="h-4 w-4 flex-shrink-0 opacity-80" />
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    );
                  })}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          ))}
        </div>
      </div>
    </div>
  );
}

    