"use client";

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  GraduationCap, 
  Search, 
  Plus, 
  Eye, 
  FileText,
  Filter,
  Calendar,
  User,
  Target,
  BookOpen
} from 'lucide-react';
import { useChildren, useAssessments, useLearningPlans } from '@/hooks/use-storage';
import { formatDate, calculateAge } from '@/lib/utils';
import Link from 'next/link';
import type { LearningP<PERSON>, Child, Assessment } from '@/lib/types';

interface LearningPlanWithDetails extends LearningPlan {
  child: Child | null;
  assessment: Assessment | null;
}

export default function LearningPlanPage() {
  const { children, loading: childrenLoading } = useChildren();
  const { assessments, loading: assessmentsLoading } = useAssessments();
  const { learningPlans, loading: plansLoading } = useLearningPlans();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedChild, setSelectedChild] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'date' | 'child' | 'recent'>('date');

  const loading = childrenLoading || assessmentsLoading || plansLoading;

  // Get all learning plans with their associated children and assessments
  const plansWithDetails: LearningPlanWithDetails[] = useMemo(() => {
    return learningPlans.map(plan => ({
      ...plan,
      child: children.find(child => child.id === plan.childId) || null,
      assessment: assessments.find(assessment => assessment.id === plan.assessmentId) || null
    }));
  }, [learningPlans, assessments, children]);

  // Filter and sort plans
  const filteredPlans = useMemo(() => {
    let filtered = plansWithDetails.filter(plan => {
      const matchesSearch = 
        (plan.child?.name.toLowerCase().includes(searchTerm.toLowerCase()) ?? false) ||
        plan.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        plan.planDetails.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesChild = selectedChild === 'all' || plan.childId === selectedChild;
      
      return matchesSearch && matchesChild;
    });

    // Sort plans
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.generatedDate).getTime() - new Date(a.generatedDate).getTime();
        case 'child':
          return (a.child?.name || '').localeCompare(b.child?.name || '');
        case 'recent':
          return new Date(b.generatedDate).getTime() - new Date(a.generatedDate).getTime();
        default:
          return 0;
      }
    });

    return filtered;
  }, [plansWithDetails, searchTerm, selectedChild, sortBy]);

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري تحميل الخطط التعليمية...</p>
          </div>
        </div>
      </div>
    );
  }

  // Get children who have assessments but no learning plans
  const childrenNeedingPlans = children.filter(child => {
    const childAssessments = assessments.filter(assessment => assessment.childId === child.id);
    const childPlans = plansWithDetails.filter(plan => plan.childId === child.id);
    return childAssessments.length > 0 && childPlans.length === 0;
  });

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold text-primary flex items-center gap-3">
            <GraduationCap className="h-8 w-8" />
            الخطط التعليمية
          </h1>
          <p className="text-muted-foreground">
            إدارة ومتابعة الخطط التعليمية للأطفال
          </p>
        </div>
        <div className="flex items-center gap-2">
          {childrenNeedingPlans.length > 0 && (
            <Badge variant="secondary" className="text-xs">
              {childrenNeedingPlans.length} طفل يحتاج خطة
            </Badge>
          )}
          <Link href="/learning-plan/new">
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              خطة جديدة
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الخطط</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{plansWithDetails.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الأطفال المخططون</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Set(plansWithDetails.map(p => p.childId)).size}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">هذا الشهر</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {plansWithDetails.filter(p => {
                const planDate = new Date(p.generatedDate);
                const now = new Date();
                return planDate.getMonth() === now.getMonth() && 
                       planDate.getFullYear() === now.getFullYear();
              }).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">تحتاج خطط</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{childrenNeedingPlans.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Children Needing Plans Alert */}
      {childrenNeedingPlans.length > 0 && (
        <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
          <CardHeader>
            <CardTitle className="text-orange-800 dark:text-orange-200 flex items-center gap-2">
              <Target className="h-5 w-5" />
              أطفال يحتاجون خطط تعليمية
            </CardTitle>
            <CardDescription className="text-orange-700 dark:text-orange-300">
              هؤلاء الأطفال لديهم تقييمات ولكن لا توجد لهم خطط تعليمية بعد
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {childrenNeedingPlans.map(child => (
                <Link key={child.id} href={`/children/${child.id}/plan`}>
                  <Badge variant="outline" className="cursor-pointer hover:bg-orange-100 dark:hover:bg-orange-900">
                    {child.name}
                  </Badge>
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            البحث والتصفية
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-2.5 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="البحث في الخطط..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-8"
                />
              </div>
            </div>
            <Select value={selectedChild} onValueChange={setSelectedChild}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="اختر طفل" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الأطفال</SelectItem>
                {children.map(child => (
                  <SelectItem key={child.id} value={child.id}>
                    {child.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={sortBy} onValueChange={(value: 'date' | 'child' | 'recent') => setSortBy(value)}>
              <SelectTrigger className="w-36">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">التاريخ</SelectItem>
                <SelectItem value="child">الطفل</SelectItem>
                <SelectItem value="recent">الأحدث</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Learning Plans Table */}
      <Card>
        <CardHeader>
          <CardTitle>قائمة الخطط التعليمية</CardTitle>
          <CardDescription>
            جميع الخطط التعليمية المنشأة في النظام
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredPlans.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-right">الطفل</TableHead>
                  <TableHead className="text-right">تاريخ الإنشاء</TableHead>
                  <TableHead className="text-right">التقييم المرجعي</TableHead>
                  <TableHead className="text-right">محتوى الخطة</TableHead>
                  <TableHead className="text-right">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPlans.map((plan) => (
                  <TableRow key={plan.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={plan.child?.avatarUrl} />
                          <AvatarFallback>
                            {plan.child?.name?.split(' ').map(n => n[0]).join('') || '؟'}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{plan.child?.name || 'غير محدد'}</div>
                          <div className="text-sm text-muted-foreground">
                            {plan.child && calculateAge(plan.child.birthDate).years} سنة
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        {formatDate(plan.generatedDate)}
                      </div>
                    </TableCell>
                    <TableCell>
                      {plan.assessment ? (
                        <div className="text-sm">
                          <div className="font-medium">
                            {formatDate(plan.assessment.assessmentDate)}
                          </div>
                          <div className="text-muted-foreground">
                            {plan.assessment.assessedSkills.length} مهارة
                          </div>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">غير محدد</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="max-w-xs">
                        <p className="text-sm line-clamp-2">
                          {plan.planDetails.length > 100 
                            ? `${plan.planDetails.substring(0, 100)}...`
                            : plan.planDetails
                          }
                        </p>
                        {plan.suggestedDailyGoals && (
                          <Badge variant="outline" className="mt-1 text-xs">
                            <Target className="h-3 w-3 mr-1" />
                            أهداف يومية
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Link href={`/children/${plan.childId}/plan`}>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button variant="outline" size="sm">
                          <FileText className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8">
              <BookOpen className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-xl font-semibold">لا توجد خطط تعليمية</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                {searchTerm || selectedChild !== 'all' 
                  ? "لا توجد خطط تطابق معايير البحث"
                  : "ابدأ بإنشاء خطة تعليمية جديدة"
                }
              </p>
              {(!searchTerm && selectedChild === 'all') && (
                <Link href="/learning-plan/new" className="mt-4 inline-block">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    إنشاء خطة جديدة
                  </Button>
                </Link>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 