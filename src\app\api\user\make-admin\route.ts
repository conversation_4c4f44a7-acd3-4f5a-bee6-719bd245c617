import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';

// POST /api/user/make-admin - Make current user admin (for testing purposes)
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Update user role to admin
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: { role: 'admin' },
      select: { id: true, email: true, role: true, name: true },
    });

    return NextResponse.json({
      message: 'User role updated to admin successfully',
      user: updatedUser,
    });
  } catch (error) {
    console.error('Error updating user role:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
