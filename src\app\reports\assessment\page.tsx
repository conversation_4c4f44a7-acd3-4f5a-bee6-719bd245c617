"use client";

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ClipboardList, 
  BarChart3, 
  <PERSON><PERSON><PERSON>, 
  TrendingUp,
  FileText,
  Calendar,
  Users,
  Target
} from 'lucide-react';
import { useChildren, useAssessments } from '@/hooks/use-api';
import { formatDate } from '@/lib/utils';
import { <PERSON>, BarC<PERSON>, Pie, Pie<PERSON><PERSON> as Recharts<PERSON>ie<PERSON>hart, Cell, XAxis, YAxis, CartesianGrid, ResponsiveContainer, Legend } from 'recharts';
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import { PORTAGE_CHECKLIST_DATA } from '@/lib/constants';

export default function AssessmentReportsPage() {
  const { data: children = [], isLoading: childrenLoading } = useChildren();
  const { data: assessments = [], isLoading: assessmentsLoading } = useAssessments();
  const [selectedPeriod, setSelectedPeriod] = useState<'month' | 'quarter' | 'year'>('month');
  const [selectedDimension, setSelectedDimension] = useState<string>('all');

  const loading = childrenLoading || assessmentsLoading;

  const allAssessments = useMemo(() => {
    return assessments;
  }, [assessments]);

  // Filter assessments by period
  const filteredAssessments = useMemo(() => {
    const now = new Date();
    const cutoffDate = new Date();
    
    switch (selectedPeriod) {
      case 'month':
        cutoffDate.setMonth(now.getMonth() - 1);
        break;
      case 'quarter':
        cutoffDate.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        cutoffDate.setFullYear(now.getFullYear() - 1);
        break;
    }

    return allAssessments.filter(assessment => 
      new Date(assessment.assessmentDate) >= cutoffDate
    );
  }, [allAssessments, selectedPeriod]);

  // Calculate dimension performance data
  const dimensionData = useMemo(() => {
    const dimensionStats: Record<string, { achieved: number; needsDevelopment: number; unclear: number; total: number }> = {};

    // Initialize all dimensions
    PORTAGE_CHECKLIST_DATA.forEach(dimension => {
      dimensionStats[dimension.name] = { achieved: 0, needsDevelopment: 0, unclear: 0, total: 0 };
    });

    filteredAssessments.forEach(assessment => {
      assessment.assessedSkills.forEach(skill => {
        // Find which dimension this skill belongs to
        for (const dimension of PORTAGE_CHECKLIST_DATA) {
          for (const subCategory of dimension.subCategories) {
            if (subCategory.skills?.some(s => s.id === skill.skillId)) {
              const stats = dimensionStats[dimension.name];
              stats.total++;
              
              switch (skill.status) {
                case 'yes':
                  stats.achieved++;
                  break;
                case 'no':
                  stats.needsDevelopment++;
                  break;
                case 'unclear':
                  stats.unclear++;
                  break;
              }
              return; // Exit loops once found
            }
          }
        }
      });
    });

    return Object.entries(dimensionStats).map(([name, stats]) => ({
      dimension: name,
      ...stats,
      achievementRate: stats.total > 0 ? Math.round((stats.achieved / stats.total) * 100) : 0
    }));
  }, [filteredAssessments]);

  // Calculate overall statistics
  const overallStats = useMemo(() => {
    const totalSkills = filteredAssessments.reduce((sum, assessment) => sum + assessment.assessedSkills.length, 0);
    const achievedSkills = filteredAssessments.reduce((sum, assessment) => 
      sum + assessment.assessedSkills.filter(skill => skill.status === 'yes').length, 0
    );
    const needsDevelopment = filteredAssessments.reduce((sum, assessment) => 
      sum + assessment.assessedSkills.filter(skill => skill.status === 'no').length, 0
    );
    const unclear = filteredAssessments.reduce((sum, assessment) => 
      sum + assessment.assessedSkills.filter(skill => skill.status === 'unclear').length, 0
    );

    return {
      totalAssessments: filteredAssessments.length,
      totalSkills,
      achievedSkills,
      needsDevelopment,
      unclear,
      achievementRate: totalSkills > 0 ? Math.round((achievedSkills / totalSkills) * 100) : 0,
      uniqueChildren: new Set(filteredAssessments.map(a => a.childId)).size
    };
  }, [filteredAssessments]);

  // Pie chart data for skill status distribution
  const skillStatusData = [
    { name: "متقن", value: overallStats.achievedSkills, fill: "hsl(var(--chart-1))" },
    { name: "يحتاج تطوير", value: overallStats.needsDevelopment, fill: "hsl(var(--chart-2))" },
    { name: "غير واضح", value: overallStats.unclear, fill: "hsl(var(--chart-3))" }
  ];

  // Assessment frequency by month
  const assessmentFrequency = useMemo(() => {
    const frequency: Record<string, number> = {};
    
    filteredAssessments.forEach(assessment => {
      const date = new Date(assessment.assessmentDate);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      frequency[monthKey] = (frequency[monthKey] || 0) + 1;
    });

    return Object.entries(frequency).map(([month, count]) => ({
      month,
      assessments: count
    })).sort((a, b) => a.month.localeCompare(b.month));
  }, [filteredAssessments]);

  const chartConfig = {
    achieved: {
      label: "متقن",
      color: "hsl(var(--chart-1))",
    },
    needsDevelopment: {
      label: "يحتاج تطوير",
      color: "hsl(var(--chart-2))",
    },
    unclear: {
      label: "غير واضح",
      color: "hsl(var(--chart-3))",
    },
    achievementRate: {
      label: "نسبة الإنجاز",
      color: "hsl(var(--chart-4))",
    },
    assessments: {
      label: "التقييمات",
      color: "hsl(var(--chart-5))",
    }
  } satisfies ChartConfig;

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري تحميل تقارير التقييم...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold text-primary flex items-center gap-3">
            <ClipboardList className="h-8 w-8" />
            تقارير التقييم
          </h1>
          <p className="text-muted-foreground">
            تحليل شامل لنتائج التقييمات والأداء
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedPeriod} onValueChange={(value: 'month' | 'quarter' | 'year') => setSelectedPeriod(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="month">شهر واحد</SelectItem>
              <SelectItem value="quarter">3 أشهر</SelectItem>
              <SelectItem value="year">سنة</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي التقييمات</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallStats.totalAssessments}</div>
            <p className="text-xs text-muted-foreground">
              في الفترة المحددة
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الأطفال المقيمون</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallStats.uniqueChildren}</div>
            <p className="text-xs text-muted-foreground">
              طفل مختلف
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المهارات المقيمة</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallStats.totalSkills}</div>
            <p className="text-xs text-muted-foreground">
              مهارة إجمالية
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">معدل النجاح</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallStats.achievementRate}%</div>
            <p className="text-xs text-muted-foreground">
              مهارات متقنة
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="dimensions" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="dimensions" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            أداء الأبعاد
          </TabsTrigger>
          <TabsTrigger value="distribution" className="flex items-center gap-2">
            <PieChart className="h-4 w-4" />
            توزيع المهارات
          </TabsTrigger>
          <TabsTrigger value="frequency" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            تكرار التقييم
          </TabsTrigger>
        </TabsList>

        {/* Dimensions Performance Tab */}
        <TabsContent value="dimensions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>أداء الأبعاد التنموية</CardTitle>
              <CardDescription>
                مقارنة مستويات الإنجاز عبر الأبعاد المختلفة
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer config={chartConfig} className="min-h-[400px] w-full">
                <BarChart data={dimensionData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                  <XAxis 
                    dataKey="dimension" 
                    stroke="hsl(var(--muted-foreground))"
                    fontSize={12}
                    angle={-45}
                    textAnchor="end"
                    height={80}
                  />
                  <YAxis 
                    stroke="hsl(var(--muted-foreground))"
                    fontSize={12}
                  />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <ChartLegend content={<ChartLegendContent />} />
                  <Bar
                    dataKey="achieved"
                    fill="var(--color-achieved)"
                    stackId="skills"
                    radius={[0, 0, 0, 0]}
                  />
                  <Bar
                    dataKey="needsDevelopment"
                    fill="var(--color-needsDevelopment)"
                    stackId="skills"
                    radius={[0, 0, 0, 0]}
                  />
                  <Bar
                    dataKey="unclear"
                    fill="var(--color-unclear)"
                    stackId="skills"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ChartContainer>
            </CardContent>
          </Card>

          {/* Dimension Achievement Rates */}
          <Card>
            <CardHeader>
              <CardTitle>معدلات الإنجاز بالأبعاد</CardTitle>
              <CardDescription>
                نسبة المهارات المتقنة في كل بُعد تنموي
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {dimensionData.map((dimension, index) => (
                  <div key={dimension.dimension} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{dimension.dimension}</span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">
                          {dimension.achieved} من {dimension.total}
                        </span>
                        <Badge variant={
                          dimension.achievementRate >= 80 ? "default" :
                          dimension.achievementRate >= 60 ? "secondary" :
                          dimension.achievementRate >= 40 ? "outline" : "destructive"
                        }>
                          {dimension.achievementRate}%
                        </Badge>
                      </div>
                    </div>
                    <div className="w-full bg-secondary rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full transition-all duration-300"
                        style={{ width: `${dimension.achievementRate}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Skills Distribution Tab */}
        <TabsContent value="distribution" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>توزيع حالة المهارات</CardTitle>
                <CardDescription>
                  النسب العامة لحالات المهارات المقيمة
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ChartContainer config={chartConfig} className="min-h-[300px] w-full">
                  <RechartsPieChart>
                    <Pie
                      data={skillStatusData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={120}
                      paddingAngle={5}
                      dataKey="value"
                      label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                    >
                      {skillStatusData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.fill} />
                      ))}
                    </Pie>
                    <ChartTooltip content={<ChartTooltipContent />} />
                  </RechartsPieChart>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>إحصائيات مفصلة</CardTitle>
                <CardDescription>
                  تفاصيل رقمية لحالات المهارات
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 rounded-lg bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-green-500" />
                      <span className="font-medium">مهارات متقنة</span>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-600">{overallStats.achievedSkills}</div>
                      <div className="text-sm text-green-600">
                        {overallStats.totalSkills > 0 ? Math.round((overallStats.achievedSkills / overallStats.totalSkills) * 100) : 0}%
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-3 rounded-lg bg-orange-50 dark:bg-orange-950 border border-orange-200 dark:border-orange-800">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-orange-500" />
                      <span className="font-medium">تحتاج تطوير</span>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-orange-600">{overallStats.needsDevelopment}</div>
                      <div className="text-sm text-orange-600">
                        {overallStats.totalSkills > 0 ? Math.round((overallStats.needsDevelopment / overallStats.totalSkills) * 100) : 0}%
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-950 border border-gray-200 dark:border-gray-800">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-gray-500" />
                      <span className="font-medium">غير واضحة</span>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-gray-600">{overallStats.unclear}</div>
                      <div className="text-sm text-gray-600">
                        {overallStats.totalSkills > 0 ? Math.round((overallStats.unclear / overallStats.totalSkills) * 100) : 0}%
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Assessment Frequency Tab */}
        <TabsContent value="frequency" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>تكرار التقييمات الشهرية</CardTitle>
              <CardDescription>
                عدد التقييمات المنجزة كل شهر
              </CardDescription>
            </CardHeader>
            <CardContent>
              {assessmentFrequency.length > 0 ? (
                <ChartContainer config={chartConfig} className="min-h-[400px] w-full">
                  <BarChart data={assessmentFrequency}>
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                    <XAxis 
                      dataKey="month" 
                      stroke="hsl(var(--muted-foreground))"
                      fontSize={12}
                    />
                    <YAxis 
                      stroke="hsl(var(--muted-foreground))"
                      fontSize={12}
                    />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Bar
                      dataKey="assessments"
                      fill="var(--color-assessments)"
                      radius={[4, 4, 0, 0]}
                    />
                  </BarChart>
                </ChartContainer>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">لا توجد بيانات كافية لعرض التكرار</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Export Actions */}
      <div className="flex justify-center gap-4">
        <Button variant="outline">
          تصدير البيانات
        </Button>
        <Button>
          إنشاء تقرير مفصل
        </Button>
      </div>
    </div>
  );
} 