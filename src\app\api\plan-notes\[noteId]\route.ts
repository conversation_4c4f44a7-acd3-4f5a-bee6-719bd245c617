import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

export async function GET(
  request: Request,
  { params }: { params: { noteId: string } }
) {
  const planNote = await prisma.planNote.findUnique({
    where: { id: params.noteId },
  });
  return NextResponse.json(planNote);
}

export async function PUT(
  request: Request,
  { params }: { params: { noteId: string } }
) {
  const data = await request.json();
  const planNote = await prisma.planNote.update({
    where: { id: params.noteId },
    data,
  });
  return NextResponse.json(planNote);
}

export async function DELETE(
  request: Request,
  { params }: { params: { noteId: string } }
) {
  await prisma.planNote.delete({
    where: { id: params.noteId },
  });
  return new Response(null, { status: 204 });
}
