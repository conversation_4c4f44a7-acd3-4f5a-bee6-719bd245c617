"use client";

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { 
  FileText, 
  Users, 
  TrendingUp, 
  Award,
  Calendar,
  Target,
  BookOpen,
  BarChart3,
  Clock,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';
import { useChildren, useAssessments, useLearningPlans } from '@/hooks/use-storage';
import { calculateAge, formatDate } from '@/lib/utils';
import Link from 'next/link';

export default function SummaryReportsPage() {
  const { children, loading: childrenLoading } = useChildren();
  const { assessments, loading: assessmentsLoading } = useAssessments();
  const { learningPlans, loading: plansLoading } = useLearningPlans();
  const [selectedPeriod, setSelectedPeriod] = useState<'month' | 'quarter' | 'year'>('month');

  const loading = childrenLoading || assessmentsLoading || plansLoading;

  // Get all data
  const allAssessments = useMemo(() => assessments, [assessments]);
  const allPlans = useMemo(() => learningPlans, [learningPlans]);

  // Filter data by period
  const filteredData = useMemo(() => {
    const now = new Date();
    const cutoffDate = new Date();
    
    switch (selectedPeriod) {
      case 'month':
        cutoffDate.setMonth(now.getMonth() - 1);
        break;
      case 'quarter':
        cutoffDate.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        cutoffDate.setFullYear(now.getFullYear() - 1);
        break;
    }

    return {
      assessments: allAssessments.filter(a => new Date(a.assessmentDate) >= cutoffDate),
      plans: allPlans.filter(p => new Date(p.generatedDate) >= cutoffDate)
    };
  }, [allAssessments, allPlans, selectedPeriod]);

  // Calculate comprehensive statistics
  const overallStats = useMemo(() => {
    const totalChildren = children.length;
    const assessedChildren = new Set(allAssessments.map(a => a.childId)).size;
    const childrenWithPlans = new Set(allPlans.map(p => p.childId)).size;
    
    const totalSkills = allAssessments.reduce((sum, a) => sum + a.assessedSkills.length, 0);
    const achievedSkills = allAssessments.reduce((sum, a) => 
      sum + a.assessedSkills.filter(s => s.status === 'yes').length, 0
    );
    
    const recentAssessments = filteredData.assessments.length;
    const recentPlans = filteredData.plans.length;
    
    return {
      totalChildren,
      assessedChildren,
      childrenWithPlans,
      unassessedChildren: totalChildren - assessedChildren,
      childrenNeedingPlans: assessedChildren - childrenWithPlans,
      totalAssessments: allAssessments.length,
      totalPlans: allPlans.length,
      totalSkills,
      achievedSkills,
      achievementRate: totalSkills > 0 ? Math.round((achievedSkills / totalSkills) * 100) : 0,
      recentAssessments,
      recentPlans,
      averageSkillsPerAssessment: allAssessments.length > 0 ? Math.round(totalSkills / allAssessments.length) : 0
    };
  }, [children, allAssessments, allPlans, filteredData]);

  // Get top performing children
  const topPerformers = useMemo(() => {
    return children.map(child => {
      const childAssessments = allAssessments.filter(a => a.childId === child.id);
      
      if (childAssessments.length === 0) {
        return { child, score: 0, assessmentCount: 0, lastAssessment: null };
      }

      const latestAssessment = childAssessments.sort((a, b) => 
        new Date(b.assessmentDate).getTime() - new Date(a.assessmentDate).getTime()
      )[0];

      const totalSkills = latestAssessment.assessedSkills.length;
      const achievedSkills = latestAssessment.assessedSkills.filter(s => s.status === 'yes').length;
      const score = totalSkills > 0 ? Math.round((achievedSkills / totalSkills) * 100) : 0;

      return {
        child,
        score,
        assessmentCount: childAssessments.length,
        lastAssessment: latestAssessment.assessmentDate,
        totalSkills,
        achievedSkills
      };
    }).sort((a, b) => b.score - a.score).slice(0, 5);
  }, [children, allAssessments]);

  // Get recent activity
  const recentActivity = useMemo(() => {
    const activities: Array<{
      type: 'assessment' | 'plan';
      date: string;
      childName: string;
      details: string;
    }> = [];

    // Add recent assessments
    filteredData.assessments.forEach(assessment => {
      const child = children.find(c => c.id === assessment.childId);
      if (child) {
        activities.push({
          type: 'assessment',
          date: assessment.assessmentDate,
          childName: child.name,
          details: `تقييم جديد - ${assessment.assessedSkills.length} مهارة`
        });
      }
    });

    // Add recent plans
    filteredData.plans.forEach(plan => {
      const child = children.find(c => c.id === plan.childId);
      if (child) {
        activities.push({
          type: 'plan',
          date: plan.generatedDate,
          childName: child.name,
          details: 'خطة تعليمية جديدة'
        });
      }
    });

    return activities.sort((a, b) => 
      new Date(b.date).getTime() - new Date(a.date).getTime()
    ).slice(0, 10);
  }, [filteredData, children]);

  // Calculate alerts and recommendations
  const alerts = useMemo(() => {
    const alertList: Array<{
      type: 'warning' | 'info' | 'success';
      title: string;
      description: string;
      action?: string;
      actionLink?: string;
    }> = [];

    // Children without assessments
    if (overallStats.unassessedChildren > 0) {
      alertList.push({
        type: 'warning',
        title: `${overallStats.unassessedChildren} طفل بدون تقييم`,
        description: 'هناك أطفال لم يتم تقييمهم بعد',
        action: 'إجراء تقييم',
        actionLink: '/assessment/new'
      });
    }

    // Children needing learning plans
    if (overallStats.childrenNeedingPlans > 0) {
      alertList.push({
        type: 'info',
        title: `${overallStats.childrenNeedingPlans} طفل يحتاج خطة تعليمية`,
        description: 'أطفال لديهم تقييمات ولكن بدون خطط تعليمية',
        action: 'إنشاء خطة',
        actionLink: '/learning-plan'
      });
    }

    // Achievement rate feedback
    if (overallStats.achievementRate >= 80) {
      alertList.push({
        type: 'success',
        title: 'أداء ممتاز',
        description: `معدل الإنجاز العام ${overallStats.achievementRate}% - استمروا في العمل الرائع!`
      });
    } else if (overallStats.achievementRate < 60) {
      alertList.push({
        type: 'warning',
        title: 'فرصة للتحسين',
        description: `معدل الإنجاز العام ${overallStats.achievementRate}% - قد تحتاجون لمراجعة الاستراتيجيات`,
        action: 'عرض التقارير',
        actionLink: '/reports/progress'
      });
    }

    return alertList;
  }, [overallStats]);

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري تحميل التقرير الموجز...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold text-primary flex items-center gap-3">
            <FileText className="h-8 w-8" />
            التقرير الموجز
          </h1>
          <p className="text-muted-foreground">
            نظرة شاملة على أداء النظام والإحصائيات العامة
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedPeriod} onValueChange={(value: 'month' | 'quarter' | 'year') => setSelectedPeriod(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="month">شهر واحد</SelectItem>
              <SelectItem value="quarter">3 أشهر</SelectItem>
              <SelectItem value="year">سنة</SelectItem>
            </SelectContent>
          </Select>
          <Button>
            تصدير التقرير
          </Button>
        </div>
      </div>

      {/* Key Metrics Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الأطفال</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallStats.totalChildren}</div>
            <p className="text-xs text-muted-foreground">
              {overallStats.assessedChildren} مقيم • {overallStats.unassessedChildren} غير مقيم
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">معدل الإنجاز</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallStats.achievementRate}%</div>
            <p className="text-xs text-muted-foreground">
              {overallStats.achievedSkills} من {overallStats.totalSkills} مهارة
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">التقييمات</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallStats.totalAssessments}</div>
            <p className="text-xs text-muted-foreground">
              {overallStats.recentAssessments} في الفترة المحددة
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الخطط التعليمية</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallStats.totalPlans}</div>
            <p className="text-xs text-muted-foreground">
              {overallStats.recentPlans} في الفترة المحددة
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Alerts and Recommendations */}
      {alerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              تنبيهات وتوصيات
            </CardTitle>
            <CardDescription>
              نقاط مهمة تحتاج انتباهكم
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {alerts.map((alert, index) => (
                <div
                  key={index}
                  className={`flex items-start justify-between p-4 rounded-lg border ${
                    alert.type === 'warning' 
                      ? 'bg-yellow-50 border-yellow-200 dark:bg-yellow-950 dark:border-yellow-800'
                      : alert.type === 'info'
                      ? 'bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800'
                      : 'bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800'
                  }`}
                >
                  <div className="space-y-1">
                    <h4 className="font-medium">{alert.title}</h4>
                    <p className="text-sm text-muted-foreground">{alert.description}</p>
                  </div>
                  {alert.action && alert.actionLink && (
                    <Link href={alert.actionLink}>
                      <Button variant="outline" size="sm">
                        {alert.action}
                      </Button>
                    </Link>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            نظرة عامة
          </TabsTrigger>
          <TabsTrigger value="performers" className="flex items-center gap-2">
            <Award className="h-4 w-4" />
            أفضل الأداءات
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            النشاط الحديث
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>توزيع الأطفال</CardTitle>
                <CardDescription>
                  حالة التقييم والخطط التعليمية
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">أطفال مقيمون</span>
                    <span className="text-sm font-medium">{overallStats.assessedChildren}</span>
                  </div>
                  <Progress value={(overallStats.assessedChildren / overallStats.totalChildren) * 100} />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">لديهم خطط تعليمية</span>
                    <span className="text-sm font-medium">{overallStats.childrenWithPlans}</span>
                  </div>
                  <Progress value={(overallStats.childrenWithPlans / overallStats.totalChildren) * 100} />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">معدل الإنجاز العام</span>
                    <span className="text-sm font-medium">{overallStats.achievementRate}%</span>
                  </div>
                  <Progress value={overallStats.achievementRate} />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>إحصائيات المهارات</CardTitle>
                <CardDescription>
                  تفاصيل عن المهارات المقيمة
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 rounded-lg bg-secondary">
                    <div className="text-2xl font-bold text-primary">{overallStats.totalSkills}</div>
                    <div className="text-sm text-muted-foreground">إجمالي المهارات</div>
                  </div>
                  <div className="text-center p-4 rounded-lg bg-secondary">
                    <div className="text-2xl font-bold text-green-600">{overallStats.achievedSkills}</div>
                    <div className="text-sm text-muted-foreground">مهارات متقنة</div>
                  </div>
                </div>

                <div className="pt-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>متوسط المهارات لكل تقييم</span>
                    <span className="font-medium">{overallStats.averageSkillsPerAssessment}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Top Performers Tab */}
        <TabsContent value="performers" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>أفضل 5 أطفال أداءً</CardTitle>
              <CardDescription>
                الأطفال الذين حققوا أعلى نسب إنجاز
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {topPerformers.map((performer, index) => (
                  <div key={performer.child.id} className="flex items-center space-x-4 space-x-reverse">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-white text-sm font-medium">
                      {index + 1}
                    </div>
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={performer.child.avatarUrl} />
                      <AvatarFallback>
                        {performer.child.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{performer.child.name}</h4>
                          <p className="text-sm text-muted-foreground">
                            {calculateAge(performer.child.birthDate).years} سنة
                            {performer.lastAssessment && (
                              <> • آخر تقييم: {formatDate(performer.lastAssessment)}</>
                            )}
                          </p>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold">{performer.score}%</div>
                          <div className="text-sm text-muted-foreground">
                            {performer.achievedSkills} من {performer.totalSkills}
                          </div>
                        </div>
                      </div>
                      <Progress value={performer.score} className="h-2" />
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {performer.assessmentCount} تقييم
                        </div>
                        {performer.score >= 90 && (
                          <Badge variant="default" className="bg-gold text-white">
                            <Award className="h-3 w-3 mr-1" />
                            متميز
                          </Badge>
                        )}
                        {performer.score >= 80 && performer.score < 90 && (
                          <Badge variant="default" className="bg-green-500">
                            ممتاز
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Recent Activity Tab */}
        <TabsContent value="activity" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>النشاط الأخير</CardTitle>
              <CardDescription>
                آخر التقييمات والخطط التعليمية المنشأة
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recentActivity.length > 0 ? (
                <div className="space-y-4">
                  {recentActivity.map((activity, index) => (
                    <div key={index} className="flex items-center gap-3 p-3 rounded-lg border">
                      <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                        activity.type === 'assessment' 
                          ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300'
                          : 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300'
                      }`}>
                        {activity.type === 'assessment' ? (
                          <BarChart3 className="h-4 w-4" />
                        ) : (
                          <BookOpen className="h-4 w-4" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium">{activity.childName}</h4>
                            <p className="text-sm text-muted-foreground">{activity.details}</p>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {formatDate(activity.date)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Clock className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-2 text-xl font-semibold">لا يوجد نشاط حديث</h3>
                  <p className="mt-1 text-sm text-muted-foreground">
                    لا توجد أنشطة في الفترة المحددة
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>إجراءات سريعة</CardTitle>
          <CardDescription>
            روابط مباشرة للمهام الأساسية
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Link href="/children">
              <Button variant="outline" className="w-full justify-start">
                <Users className="h-4 w-4 mr-2" />
                إدارة الأطفال
              </Button>
            </Link>
            <Link href="/assessment/new">
              <Button variant="outline" className="w-full justify-start">
                <BarChart3 className="h-4 w-4 mr-2" />
                تقييم جديد
              </Button>
            </Link>
            <Link href="/learning-plan">
              <Button variant="outline" className="w-full justify-start">
                <BookOpen className="h-4 w-4 mr-2" />
                الخطط التعليمية
              </Button>
            </Link>
            <Link href="/reports/progress">
              <Button variant="outline" className="w-full justify-start">
                <TrendingUp className="h-4 w-4 mr-2" />
                تقارير التقدم
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 