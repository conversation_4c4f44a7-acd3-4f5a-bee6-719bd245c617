import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

export async function GET(
  request: Request,
  { params }: { params: { userId: string } }
) {
  const user = await prisma.user.findUnique({
    where: { id: params.userId },
  });
  return NextResponse.json(user);
}

export async function PUT(
  request: Request,
  { params }: { params: { userId: string } }
) {
  const data = await request.json();
  const user = await prisma.user.update({
    where: { id: params.userId },
    data,
  });
  return NextResponse.json(user);
}

export async function DELETE(
  request: Request,
  { params }: { params: { userId: string } }
) {
  await prisma.user.delete({
    where: { id: params.userId },
  });
  return new Response(null, { status: 204 });
}
