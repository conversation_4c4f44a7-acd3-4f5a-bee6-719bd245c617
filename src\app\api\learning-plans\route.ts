import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for learning plan data
const learningPlanSchema = z.object({
  childId: z.string().min(1, 'Child ID is required'),
  assessmentId: z.string().min(1, 'Assessment ID is required'),
  planDetails: z.string().min(1, 'Plan details are required'),
  suggestedDailyGoals: z.string().optional(),
});

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const childId = searchParams.get('childId');

    let whereClause = {};
    if (childId) {
      whereClause = { childId };
    }

    const learningPlans = await prisma.learningPlan.findMany({
      where: whereClause,
      include: {
        child: {
          select: {
            id: true,
            name: true,
            childIdNumber: true,
          },
        },
        assessment: {
          select: {
            id: true,
            assessmentDate: true,
          },
        },
      },
      orderBy: { generatedDate: 'desc' },
    });

    return NextResponse.json(learningPlans);
  } catch (error) {
    console.error('Error fetching learning plans:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only certain roles can create learning plans
    const allowedRoles = ['super_admin', 'eiu_manager', 'case_manager', 'specialist'];
    if (!allowedRoles.includes(session.user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = learningPlanSchema.parse(body);

    // Verify child and assessment exist
    const child = await prisma.child.findUnique({
      where: { id: validatedData.childId },
    });

    if (!child) {
      return NextResponse.json(
        { error: 'Child not found' },
        { status: 404 }
      );
    }

    const assessment = await prisma.assessment.findUnique({
      where: { id: validatedData.assessmentId },
    });

    if (!assessment) {
      return NextResponse.json(
        { error: 'Assessment not found' },
        { status: 404 }
      );
    }

    const learningPlan = await prisma.learningPlan.create({
      data: {
        ...validatedData,
        generatedDate: new Date(),
      },
      include: {
        child: {
          select: {
            id: true,
            name: true,
            childIdNumber: true,
          },
        },
        assessment: {
          select: {
            id: true,
            assessmentDate: true,
          },
        },
      },
    });

    return NextResponse.json(learningPlan, { status: 201 });
  } catch (error) {
    console.error('Error creating learning plan:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
