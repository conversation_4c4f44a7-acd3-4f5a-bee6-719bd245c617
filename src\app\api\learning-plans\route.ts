import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

export async function GET() {
  const learningPlans = await prisma.learningPlan.findMany();
  return NextResponse.json(learningPlans);
}

export async function POST(request: Request) {
  const data = await request.json();
  const learningPlan = await prisma.learningPlan.create({ data });
  return NextResponse.json(learningPlan, { status: 201 });
}
