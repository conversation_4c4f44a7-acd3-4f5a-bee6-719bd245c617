'use client';

import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Users, 
  FilePlus2, 
  Activity, 
  BarChart3, 
  UserPlus, 
  TrendingUp, 
  CheckCircle, 
  AlertTriangle, 
  CalendarClock, 
  Info,
  Sparkles,
  Target,
  Brain,
  Heart,
  Star
} from 'lucide-react';
import { APP_NAME, AGE_DISTRIBUTION_GROUPS } from '@/lib/constants';
import type { Child, Assessment } from '@/lib/types';
import { calculateAge, formatDate } from '@/lib/utils';
import { differenceInDays, parseISO, isWithinInterval, subDays, addMonths, isPast, isValid } from 'date-fns';
import type { ChartConfig } from "@/components/ui/chart";
import { EnhancedBreadcrumb } from '@/components/ui/enhanced-breadcrumb';
import { Loading } from '@/components/ui/loading-spinner';
import AgeDistributionChart from '@/components/dashboard/AgeDistributionChart';
import ProgressOverviewChart from '@/components/dashboard/ProgressOverviewChart';
import GenderDistributionChart from '@/components/dashboard/GenderDistributionChart';
import { useChildren, useAssessments } from '@/hooks/use-storage';

// Helper to get the latest assessment for a child
const getLatestAssessmentForChild = (childId: string, assessments: Assessment[]): Assessment | undefined => {
  return assessments
    .filter(a => a.childId === childId)
    .sort((a, b) => new Date(b.assessmentDate).getTime() - new Date(a.assessmentDate).getTime())[0];
};

export default function DashboardPage() {
  const { children, loading: childrenLoading } = useChildren();
  const { assessments, loading: assessmentsLoading } = useAssessments();

  const loading = childrenLoading || assessmentsLoading;

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <Loading>جاري تحميل بيانات لوحة التحكم...</Loading>
      </div>
    );
  }

  // Calculate analytics data
  const totalChildren = children.length;
  const maleChildren = children.filter(c => c.gender === 'male').length;
  const femaleChildren = children.filter(c => c.gender === 'female').length;

  const today = new Date();
  const newChildrenCount = children.filter(c => {
    try {
      const enrollmentDate = parseISO(c.enrollmentDate);
      return differenceInDays(today, enrollmentDate) <= 30;
    } catch (e) {
      return false;
    }
  }).length;

  const servicesEndedCount = children.filter(c => {
    const age = calculateAge(c.birthDate);
    return age.years >= 6;
  }).length;

  // Progress Overview Data
  let childrenWithMasteredGoals = 0;
  let childrenWithImplementedGoalsOnly = 0;
  let childrenWithPendingOrNoTrackableGoals = 0;

  children.forEach(child => {
    const latestAssessment = getLatestAssessmentForChild(child.id, assessments);
    if (latestAssessment) {
      const hasMastered = latestAssessment.assessedSkills.some(s => s.progressStatus === 'mastered');
      const hasImplemented = latestAssessment.assessedSkills.some(s => s.progressStatus === 'implemented');
      const hasTrackableGoals = latestAssessment.assessedSkills.some(s => s.status === 'no' || s.status === 'unclear');

      if (hasMastered) {
        childrenWithMasteredGoals++;
      } else if (hasImplemented) {
        childrenWithImplementedGoalsOnly++;
      } else {
        childrenWithPendingOrNoTrackableGoals++;
      }
    } else {
      childrenWithPendingOrNoTrackableGoals++;
    }
  });

  // Alert calculations
  const overdueReassessmentChildren: Child[] = [];
  const dueSoonReassessmentChildren: { child: Child; dueDate: string }[] = [];
  const serviceCompletionChildren: Child[] = [];
  const initialAssessmentNeededChildren: Child[] = [];

  children.forEach(child => {
    const age = calculateAge(child.birthDate, today.toISOString());
    let isServiceCompleted = false;
    if (age.years >= 6) {
      serviceCompletionChildren.push(child);
      isServiceCompleted = true;
    }

    if (!isServiceCompleted) {
      const latestAssessment = getLatestAssessmentForChild(child.id, assessments);
      if (latestAssessment) {
        try {
          const lastAssessmentDate = parseISO(latestAssessment.assessmentDate);
          if (isValid(lastAssessmentDate)) {
            const reassessmentDueDate = addMonths(lastAssessmentDate, 4);
            const formattedDueDate = formatDate(reassessmentDueDate.toISOString());

            if (isPast(reassessmentDueDate)) {
              overdueReassessmentChildren.push(child);
            } else {
              const daysUntilReassessment = differenceInDays(reassessmentDueDate, today);
              if (daysUntilReassessment <= 14 && daysUntilReassessment >= 0) {
                dueSoonReassessmentChildren.push({ child, dueDate: formattedDueDate });
              }
            }
          }
        } catch (error) {
          // Handle silently
        }
      } else {
        initialAssessmentNeededChildren.push(child);
      }
    }
  });

  const ageDistributionData = AGE_DISTRIBUTION_GROUPS.map(group => ({ 
    ...group, 
    count: 0, 
    fill: `hsl(var(--chart-${(AGE_DISTRIBUTION_GROUPS.indexOf(group) % 5) + 1}))` 
  }));
  
  children.forEach(child => {
    const age = calculateAge(child.birthDate);
    const ageInMonths = age.years * 12 + age.months;
    const groupFound = ageDistributionData.find(group => ageInMonths >= group.minMonths && ageInMonths < group.maxMonths);
    if (groupFound) {
      groupFound.count++;
    }
  });

  const ageChartConfig = {
    count: { label: "عدد الأطفال" },
    ...ageDistributionData.reduce((acc, group) => {
      acc[group.label] = { label: group.label, color: group.fill };
      return acc;
    }, {} as Record<string, { label: string; color: string }>)
  } satisfies ChartConfig;

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="text-center space-y-4 py-8">
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-primary to-primary/80 text-primary-foreground shadow-lg">
            <Brain className="h-6 w-6" />
          </div>
          <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
            {APP_NAME}
          </h1>
        </div>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          نظام شامل لتقييم وتنمية الطفولة المبكرة بناءً على منهج بورتيج
        </p>
        <div className="flex items-center justify-center gap-2 pt-4">
          <Badge variant="secondary" className="flex items-center gap-1">
            <Star className="h-3 w-3" />
            نسخة محدثة
          </Badge>
          <Badge variant="outline" className="flex items-center gap-1">
            <Heart className="h-3 w-3" />
            تصميم جديد
          </Badge>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="card-enhanced hover:shadow-lg transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الأطفال</CardTitle>
            <Users className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-primary">{totalChildren}</div>
            <p className="text-xs text-muted-foreground mt-1">
              المسجلين في البرنامج
            </p>
          </CardContent>
        </Card>

        <Card className="card-enhanced hover:shadow-lg transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">تسجيلات جديدة</CardTitle>
            <UserPlus className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{newChildrenCount}</div>
            <p className="text-xs text-muted-foreground mt-1">
              خلال الشهر الماضي
            </p>
          </CardContent>
        </Card>

        <Card className="card-enhanced hover:shadow-lg transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الأهداف المحققة</CardTitle>
            <Target className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{childrenWithMasteredGoals}</div>
            <p className="text-xs text-muted-foreground mt-1">
              أطفال حققوا أهدافهم
            </p>
          </CardContent>
        </Card>

        <Card className="card-enhanced hover:shadow-lg transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إنهاء الخدمات</CardTitle>
            <CheckCircle className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{servicesEndedCount}</div>
            <p className="text-xs text-muted-foreground mt-1">
              بلغوا 6 سنوات
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Actions */}
        <Card className="card-enhanced">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-primary" />
              الإجراءات السريعة
            </CardTitle>
            <CardDescription>
              الوصول السريع للمهام الأساسية
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button asChild className="w-full justify-start h-12" size="lg">
              <Link href="/children/new" className="flex items-center gap-3">
                <UserPlus className="h-5 w-5" />
                <div className="text-right">
                  <div className="font-medium">إضافة طفل جديد</div>
                  <div className="text-xs opacity-70">تسجيل طفل في البرنامج</div>
                </div>
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="w-full justify-start h-12" size="lg">
              <Link href="/children" className="flex items-center gap-3">
                <Users className="h-5 w-5" />
                <div className="text-right">
                  <div className="font-medium">إدارة الأطفال</div>
                  <div className="text-xs opacity-70">عرض وتعديل بيانات الأطفال</div>
                </div>
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="w-full justify-start h-12" size="lg">
              <Link href="/assessment/new" className="flex items-center gap-3">
                <FilePlus2 className="h-5 w-5" />
                <div className="text-right">
                  <div className="font-medium">تقييم جديد</div>
                  <div className="text-xs opacity-70">إجراء تقييم للطفل</div>
                </div>
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="w-full justify-start h-12" size="lg">
              <Link href="/reports" className="flex items-center gap-3">
                <BarChart3 className="h-5 w-5" />
                <div className="text-right">
                  <div className="font-medium">التقارير</div>
                  <div className="text-xs opacity-70">عرض التقارير والإحصائيات</div>
                </div>
              </Link>
            </Button>
          </CardContent>
        </Card>

        {/* Progress Overview */}
        <Card className="card-enhanced">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-primary" />
              نظرة عامة على التقدم
            </CardTitle>
            <CardDescription>
              توزيع الأطفال حسب مستوى الإنجاز
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ProgressOverviewChart 
              mastered={childrenWithMasteredGoals}
              implemented={childrenWithImplementedGoalsOnly} 
              pending={childrenWithPendingOrNoTrackableGoals}
            />
          </CardContent>
        </Card>

        {/* Age Distribution */}
        <Card className="card-enhanced">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-primary" />
              التوزيع العمري
            </CardTitle>
            <CardDescription>
              توزيع الأطفال حسب الفئات العمرية
            </CardDescription>
          </CardHeader>
          <CardContent>
            <AgeDistributionChart 
              data={ageDistributionData} 
              config={ageChartConfig}
            />
          </CardContent>
        </Card>
      </div>

      {/* Alerts and Notifications */}
      {(overdueReassessmentChildren.length > 0 || 
        dueSoonReassessmentChildren.length > 0 || 
        initialAssessmentNeededChildren.length > 0 || 
        serviceCompletionChildren.length > 0) && (
        <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950/30">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-700 dark:text-orange-400">
              <AlertTriangle className="h-5 w-5" />
              تنبيهات وإشعارات هامة
            </CardTitle>
            <CardDescription>
              عناصر تحتاج إلى متابعة فورية
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {overdueReassessmentChildren.length > 0 && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg dark:bg-red-950/30 dark:border-red-800">
                <h4 className="font-semibold text-red-700 dark:text-red-400 mb-2 flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  إعادة تقييم مطلوبة فورًا ({overdueReassessmentChildren.length})
                </h4>
                <div className="space-y-2">
                  {overdueReassessmentChildren.slice(0, 3).map(child => (
                    <div key={child.id} className="text-sm">
                      <Link 
                        href={`/children/${child.id}/assessment`}
                        className="text-red-600 hover:text-red-800 font-medium"
                      >
                        {child.name}
                      </Link>
                      <span className="text-red-500 mr-2">- آخر تقييم منذ أكثر من 4 أشهر</span>
                    </div>
                  ))}
                  {overdueReassessmentChildren.length > 3 && (
                    <p className="text-sm text-red-600">
                      وهناك {overdueReassessmentChildren.length - 3} آخرين...
                    </p>
                  )}
                </div>
              </div>
            )}

            {dueSoonReassessmentChildren.length > 0 && (
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg dark:bg-yellow-950/30 dark:border-yellow-800">
                <h4 className="font-semibold text-yellow-700 dark:text-yellow-400 mb-2 flex items-center gap-2">
                  <CalendarClock className="h-4 w-4" />
                  إعادة تقييم قريبة ({dueSoonReassessmentChildren.length})
                </h4>
                <div className="space-y-2">
                  {dueSoonReassessmentChildren.slice(0, 3).map(({ child, dueDate }) => (
                    <div key={child.id} className="text-sm">
                      <Link 
                        href={`/children/${child.id}/assessment`}
                        className="text-yellow-600 hover:text-yellow-800 font-medium"
                      >
                        {child.name}
                      </Link>
                      <span className="text-yellow-600 mr-2">- موعد الإعادة: {dueDate}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {initialAssessmentNeededChildren.length > 0 && (
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg dark:bg-blue-950/30 dark:border-blue-800">
                <h4 className="font-semibold text-blue-700 dark:text-blue-400 mb-2 flex items-center gap-2">
                  <Info className="h-4 w-4" />
                  تقييم أولي مطلوب ({initialAssessmentNeededChildren.length})
                </h4>
                <div className="space-y-2">
                  {initialAssessmentNeededChildren.slice(0, 3).map(child => (
                    <div key={child.id} className="text-sm">
                      <Link 
                        href={`/children/${child.id}/assessment/new`}
                        className="text-blue-600 hover:text-blue-800 font-medium"
                      >
                        {child.name}
                      </Link>
                      <span className="text-blue-600 mr-2">- لا يوجد تقييم مسجل</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="card-enhanced">
          <CardHeader>
            <CardTitle>التوزيع حسب الجنس</CardTitle>
            <CardDescription>
              نسبة الذكور والإناث في البرنامج
            </CardDescription>
          </CardHeader>
          <CardContent>
            <GenderDistributionChart 
              maleCount={maleChildren}
              femaleCount={femaleChildren}
            />
          </CardContent>
        </Card>

        <Card className="card-enhanced">
          <CardHeader>
            <CardTitle>الأنشطة الأخيرة</CardTitle>
            <CardDescription>
              آخر الأنشطة والتحديثات في النظام
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground">
                {children.length === 0 ? (
                  <div className="text-center py-8">
                    <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>لا توجد بيانات للعرض</p>
                    <p className="text-xs mt-1">ابدأ بإضافة أطفال جدد للبرنامج</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
                        <Users className="h-4 w-4" />
                      </div>
                      <div>
                        <p className="font-medium">إجمالي الأطفال المسجلين</p>
                        <p className="text-xs text-muted-foreground">{totalChildren} طفل في البرنامج</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-500 text-white">
                        <CheckCircle className="h-4 w-4" />
                      </div>
                      <div>
                        <p className="font-medium">التقييمات المكتملة</p>
                        <p className="text-xs text-muted-foreground">{assessments.length} تقييم مسجل</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-orange-500 text-white">
                        <Target className="h-4 w-4" />
                      </div>
                      <div>
                        <p className="font-medium">الأهداف المحققة</p>
                        <p className="text-xs text-muted-foreground">{childrenWithMasteredGoals} طفل حقق أهدافه</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

