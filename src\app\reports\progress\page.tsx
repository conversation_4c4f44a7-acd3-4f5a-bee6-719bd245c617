"use client";

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  Calendar, 
  Users, 
  Target,
  BarChart3,
  LineChart,
  Award,
  Clock
} from 'lucide-react';
import { useChildren, useAssessments } from '@/hooks/use-storage';
import { calculateAge, formatDate } from '@/lib/utils';
import { Area, AreaChart, Bar, BarChart, Line, LineChart as RechartsLine<PERSON>hart, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, ResponsiveContainer } from 'recharts';
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import type { Child, Assessment } from '@/lib/types';

export default function ProgressReportsPage() {
  const { children, loading: childrenLoading } = useChildren();
  const { assessments, loading: assessmentsLoading } = useAssessments();
  const [selectedChild, setSelectedChild] = useState<string>('all');
  const [timeRange, setTimeRange] = useState<'3months' | '6months' | '1year'>('6months');

  const loading = childrenLoading || assessmentsLoading;

  // Get all assessments
  const allAssessments = useMemo(() => {
    return assessments;
  }, [assessments]);

  // Filter assessments by selected child and time range
  const filteredAssessments = useMemo(() => {
    let filtered = allAssessments;

    if (selectedChild !== 'all') {
      filtered = filtered.filter(assessment => assessment.childId === selectedChild);
    }

    // Filter by time range
    const now = new Date();
    const cutoffDate = new Date();
    switch (timeRange) {
      case '3months':
        cutoffDate.setMonth(now.getMonth() - 3);
        break;
      case '6months':
        cutoffDate.setMonth(now.getMonth() - 6);
        break;
      case '1year':
        cutoffDate.setFullYear(now.getFullYear() - 1);
        break;
    }

    filtered = filtered.filter(assessment => 
      new Date(assessment.assessmentDate) >= cutoffDate
    );

    return filtered.sort((a, b) => 
      new Date(a.assessmentDate).getTime() - new Date(b.assessmentDate).getTime()
    );
  }, [allAssessments, selectedChild, timeRange]);

  // Calculate progress data for charts
  const progressChartData = useMemo(() => {
    const monthlyData: Record<string, any> = {};

    filteredAssessments.forEach(assessment => {
      const date = new Date(assessment.assessmentDate);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = {
          month: monthKey,
          totalSkills: 0,
          achievedSkills: 0,
          assessmentCount: 0
        };
      }

      monthlyData[monthKey].totalSkills += assessment.assessedSkills.length;
      monthlyData[monthKey].achievedSkills += assessment.assessedSkills.filter(s => s.status === 'yes').length;
      monthlyData[monthKey].assessmentCount += 1;
    });

    return Object.values(monthlyData).map(data => ({
      ...data,
      progressPercentage: data.totalSkills > 0 ? Math.round((data.achievedSkills / data.totalSkills) * 100) : 0
    }));
  }, [filteredAssessments]);

  // Calculate child progress statistics
  const childrenProgress = useMemo(() => {
    return children.map(child => {
      const childAssessments = allAssessments.filter(a => a.childId === child.id);
      
      if (childAssessments.length === 0) {
        return { child, progress: 0, totalSkills: 0, achievedSkills: 0, lastAssessmentDate: null };
      }

      const latestAssessment = childAssessments.sort((a, b) => 
        new Date(b.assessmentDate).getTime() - new Date(a.assessmentDate).getTime()
      )[0];

      const totalSkills = latestAssessment.assessedSkills.length;
      const achievedSkills = latestAssessment.assessedSkills.filter(s => s.status === 'yes').length;
      const progress = totalSkills > 0 ? Math.round((achievedSkills / totalSkills) * 100) : 0;

      return {
        child,
        progress,
        totalSkills,
        achievedSkills,
        lastAssessmentDate: latestAssessment.assessmentDate,
        assessmentCount: childAssessments.length
      };
    }).sort((a, b) => b.progress - a.progress);
  }, [children, allAssessments]);

  const chartConfig = {
    progressPercentage: {
      label: "نسبة التقدم",
      color: "hsl(var(--chart-1))",
    },
    achievedSkills: {
      label: "المهارات المتقنة",
      color: "hsl(var(--chart-2))",
    },
    totalSkills: {
      label: "إجمالي المهارات",
      color: "hsl(var(--chart-3))",
    }
  } satisfies ChartConfig;

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري تحميل تقارير التقدم...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold text-primary flex items-center gap-3">
            <TrendingUp className="h-8 w-8" />
            تقارير التقدم
          </h1>
          <p className="text-muted-foreground">
            متابعة وتحليل تقدم الأطفال عبر الزمن
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedChild} onValueChange={setSelectedChild}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="اختر طفل" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع الأطفال</SelectItem>
              {children.map(child => (
                <SelectItem key={child.id} value={child.id}>
                  {child.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={timeRange} onValueChange={(value: '3months' | '6months' | '1year') => setTimeRange(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="3months">3 أشهر</SelectItem>
              <SelectItem value="6months">6 أشهر</SelectItem>
              <SelectItem value="1year">سنة</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي التقييمات</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredAssessments.length}</div>
            <p className="text-xs text-muted-foreground">
              في الفترة المحددة
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الأطفال النشطون</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Set(filteredAssessments.map(a => a.childId)).size}
            </div>
            <p className="text-xs text-muted-foreground">
              لديهم تقييمات
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">متوسط التقدم</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {childrenProgress.length > 0 
                ? Math.round(childrenProgress.reduce((sum, cp) => sum + cp.progress, 0) / childrenProgress.length)
                : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              عبر جميع الأطفال
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">أفضل أداء</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {childrenProgress.length > 0 ? `${childrenProgress[0].progress}%` : '0%'}
            </div>
            <p className="text-xs text-muted-foreground">
              {childrenProgress.length > 0 ? childrenProgress[0].child.name : 'لا يوجد'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <LineChart className="h-4 w-4" />
            نظرة عامة
          </TabsTrigger>
          <TabsTrigger value="individual" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            تقدم فردي
          </TabsTrigger>
          <TabsTrigger value="comparison" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            مقارنة
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>اتجاه التقدم العام</CardTitle>
              <CardDescription>
                تطور نسب النجاح عبر الفترة الزمنية المحددة
              </CardDescription>
            </CardHeader>
            <CardContent>
              {progressChartData.length > 0 ? (
                <ChartContainer config={chartConfig} className="min-h-[400px] w-full">
                  <AreaChart data={progressChartData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                    <XAxis 
                      dataKey="month" 
                      stroke="hsl(var(--muted-foreground))"
                      fontSize={12}
                    />
                    <YAxis 
                      stroke="hsl(var(--muted-foreground))"
                      fontSize={12}
                    />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Area
                      type="monotone"
                      dataKey="progressPercentage"
                      stroke="var(--color-progressPercentage)"
                      fill="var(--color-progressPercentage)"
                      fillOpacity={0.6}
                    />
                  </AreaChart>
                </ChartContainer>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">لا توجد بيانات كافية لعرض الرسم البياني</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Individual Progress Tab */}
        <TabsContent value="individual" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>تقدم الأطفال الفردي</CardTitle>
              <CardDescription>
                نظرة تفصيلية على تقدم كل طفل
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {childrenProgress.map((childProgress) => (
                  <div key={childProgress.child.id} className="flex items-center space-x-4 space-x-reverse">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={childProgress.child.avatarUrl} />
                      <AvatarFallback>
                        {childProgress.child.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{childProgress.child.name}</h4>
                          <p className="text-sm text-muted-foreground">
                            {calculateAge(childProgress.child.birthDate).years} سنة
                            {childProgress.lastAssessmentDate && (
                              <> • آخر تقييم: {formatDate(childProgress.lastAssessmentDate)}</>
                            )}
                          </p>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold">{childProgress.progress}%</div>
                          <div className="text-sm text-muted-foreground">
                            {childProgress.achievedSkills} من {childProgress.totalSkills}
                          </div>
                        </div>
                      </div>
                      <Progress value={childProgress.progress} className="h-2" />
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {childProgress.assessmentCount} تقييم
                        </div>
                        {childProgress.progress >= 80 && (
                          <Badge variant="default" className="bg-green-500">
                            <Award className="h-3 w-3 mr-1" />
                            أداء ممتاز
                          </Badge>
                        )}
                        {childProgress.progress >= 60 && childProgress.progress < 80 && (
                          <Badge variant="secondary" className="bg-blue-500 text-white">
                            أداء جيد
                          </Badge>
                        )}
                        {childProgress.progress < 40 && (
                          <Badge variant="outline" className="border-orange-500 text-orange-600">
                            يحتاج متابعة
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Comparison Tab */}
        <TabsContent value="comparison" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>مقارنة الأداء</CardTitle>
              <CardDescription>
                مقارنة مستويات تقدم الأطفال
              </CardDescription>
            </CardHeader>
            <CardContent>
              {childrenProgress.length > 0 ? (
                <ChartContainer config={chartConfig} className="min-h-[400px] w-full">
                  <BarChart 
                    data={childrenProgress.map(cp => ({
                      name: cp.child.name.split(' ')[0], // First name only for space
                      progress: cp.progress,
                      achieved: cp.achievedSkills,
                      total: cp.totalSkills
                    }))}
                    margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                    <XAxis 
                      dataKey="name" 
                      stroke="hsl(var(--muted-foreground))"
                      fontSize={12}
                      angle={-45}
                      textAnchor="end"
                      height={80}
                    />
                    <YAxis 
                      stroke="hsl(var(--muted-foreground))"
                      fontSize={12}
                    />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Bar
                      dataKey="progress"
                      fill="var(--color-progressPercentage)"
                      radius={[4, 4, 0, 0]}
                    />
                  </BarChart>
                </ChartContainer>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">لا توجد بيانات للمقارنة</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Export Actions */}
      <div className="flex justify-center gap-4">
        <Button variant="outline">
          تصدير التقرير
        </Button>
        <Button>
          إنشاء تقرير مفصل
        </Button>
      </div>
    </div>
  );
} 