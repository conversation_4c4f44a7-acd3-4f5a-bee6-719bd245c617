"use client";

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger, DialogClose, DialogFooter } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { StickyNote, Save, Users, School } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { usePlanNotes } from '@/hooks/use-storage';
import type { PlanNote } from '@/lib/types';

interface GlobalPlanNotesProps {
  childId: string;
  childName: string;
}

export default function GlobalPlanNotes({ childId, childName }: GlobalPlanNotesProps) {
  const { toast } = useToast();
  const { addPlanNote, updatePlanNote, getGlobalPlanNoteByType } = usePlanNotes(childId);

  // Global Notes States
  const [globalFamilyNoteText, setGlobalFamilyNoteText] = useState('');
  const [globalPreschoolNoteText, setGlobalPreschoolNoteText] = useState('');
  const [isGlobalFamilyNoteDialogOpen, setIsGlobalFamilyNoteDialogOpen] = useState(false);
  const [isGlobalPreschoolNoteDialogOpen, setIsGlobalPreschoolNoteDialogOpen] = useState(false);

  // Load existing global notes
  useEffect(() => {
    const globalFamilyNote = getGlobalPlanNoteByType('family');
    const globalPreschoolNote = getGlobalPlanNoteByType('preschool');
    
    if (globalFamilyNote) {
      setGlobalFamilyNoteText(globalFamilyNote.notes);
    }
    if (globalPreschoolNote) {
      setGlobalPreschoolNoteText(globalPreschoolNote.notes);
    }
  }, [getGlobalPlanNoteByType]);

  // Global Notes Functions
  const handleSaveGlobalFamilyNote = () => {
    if (!globalFamilyNoteText.trim()) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال نص الملاحظة.",
        variant: "destructive",
      });
      return;
    }

    const existingNote = getGlobalPlanNoteByType('family');
    const planNote: PlanNote = {
      id: existingNote?.id || `global-plan-note-${Date.now()}`,
      childId,
      planType: 'family',
      notes: globalFamilyNoteText.trim(),
      isGlobal: true,
      createdDate: existingNote?.createdDate || new Date().toISOString().split('T')[0],
      lastModified: new Date().toISOString().split('T')[0],
    };

    const success = existingNote ? updatePlanNote(planNote) : addPlanNote(planNote);
    
    if (success) {
      toast({
        title: "تم الحفظ",
        description: "تم حفظ الملاحظة العامة للخطة الأسرية بنجاح.",
      });
      setIsGlobalFamilyNoteDialogOpen(false);
    } else {
      toast({
        title: "خطأ",
        description: "فشل في حفظ الملاحظة.",
        variant: "destructive",
      });
    }
  };

  const handleSaveGlobalPreschoolNote = () => {
    if (!globalPreschoolNoteText.trim()) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال نص الملاحظة.",
        variant: "destructive",
      });
      return;
    }

    const existingNote = getGlobalPlanNoteByType('preschool');
    const planNote: PlanNote = {
      id: existingNote?.id || `global-plan-note-${Date.now()}`,
      childId,
      planType: 'preschool',
      notes: globalPreschoolNoteText.trim(),
      isGlobal: true,
      createdDate: existingNote?.createdDate || new Date().toISOString().split('T')[0],
      lastModified: new Date().toISOString().split('T')[0],
    };

    const success = existingNote ? updatePlanNote(planNote) : addPlanNote(planNote);
    
    if (success) {
      toast({
        title: "تم الحفظ",
        description: "تم حفظ الملاحظة العامة لخطة الروضة/الحضانة بنجاح.",
      });
      setIsGlobalPreschoolNoteDialogOpen(false);
    } else {
      toast({
        title: "خطأ",
        description: "فشل في حفظ الملاحظة.",
        variant: "destructive",
      });
    }
  };

  const globalFamilyNote = getGlobalPlanNoteByType('family');
  const globalPreschoolNote = getGlobalPlanNoteByType('preschool');

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <StickyNote className="h-5 w-5 text-primary" />
          ملاحظات عامة للخطط
        </CardTitle>
        <CardDescription>
          ملاحظات تطبق على جميع المهارات في الخطط الأسرية وخطط الروضة/الحضانة للطفل {childName}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Global Family Plan Notes */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="font-medium flex items-center gap-2">
                <Users className="h-4 w-4 text-blue-500" />
                الخطة الأسرية
              </h4>
              {globalFamilyNote && (
                <Badge variant="outline" className="text-xs">
                  آخر تحديث: {new Date(globalFamilyNote.lastModified).toLocaleDateString('ar-SA')}
                </Badge>
              )}
            </div>
            
            <Dialog open={isGlobalFamilyNoteDialogOpen} onOpenChange={setIsGlobalFamilyNoteDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="w-full relative">
                  <StickyNote className="ml-2 h-4 w-4 text-blue-500" />
                  {globalFamilyNote ? 'تعديل الملاحظة العامة' : 'إضافة ملاحظة عامة'}
                  {globalFamilyNote && (
                    <div className="absolute -top-1 -right-1 h-2 w-2 bg-blue-500 rounded-full"></div>
                  )}
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>ملاحظة عامة للخطة الأسرية</DialogTitle>
                  <DialogDescription>
                    هذه الملاحظة ستطبق على جميع المهارات في الخطط الأسرية للطفل {childName}
                  </DialogDescription>
                </DialogHeader>
                <div className="py-4">
                  <Label htmlFor="globalFamilyNotes">الملاحظة العامة</Label>
                  <Textarea
                    id="globalFamilyNotes"
                    value={globalFamilyNoteText}
                    onChange={(e) => setGlobalFamilyNoteText(e.target.value)}
                    placeholder="أضف ملاحظة عامة تطبق على جميع الخطط الأسرية..."
                    className="min-h-[120px]"
                  />
                </div>
                <DialogFooter>
                  <DialogClose asChild><Button variant="outline">إلغاء</Button></DialogClose>
                  <Button onClick={handleSaveGlobalFamilyNote}><Save className="ml-2 h-4 w-4" /> حفظ الملاحظة</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {globalFamilyNote && (
              <div className="text-sm p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-blue-800">{globalFamilyNote.notes}</p>
              </div>
            )}
          </div>

          {/* Global Preschool Plan Notes */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="font-medium flex items-center gap-2">
                <School className="h-4 w-4 text-green-500" />
                خطة الروضة/الحضانة
              </h4>
              {globalPreschoolNote && (
                <Badge variant="outline" className="text-xs">
                  آخر تحديث: {new Date(globalPreschoolNote.lastModified).toLocaleDateString('ar-SA')}
                </Badge>
              )}
            </div>
            
            <Dialog open={isGlobalPreschoolNoteDialogOpen} onOpenChange={setIsGlobalPreschoolNoteDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="w-full relative">
                  <StickyNote className="ml-2 h-4 w-4 text-green-500" />
                  {globalPreschoolNote ? 'تعديل الملاحظة العامة' : 'إضافة ملاحظة عامة'}
                  {globalPreschoolNote && (
                    <div className="absolute -top-1 -right-1 h-2 w-2 bg-green-500 rounded-full"></div>
                  )}
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>ملاحظة عامة لخطة الروضة/الحضانة</DialogTitle>
                  <DialogDescription>
                    هذه الملاحظة ستطبق على جميع المهارات في خطط الروضة/الحضانة للطفل {childName}
                  </DialogDescription>
                </DialogHeader>
                <div className="py-4">
                  <Label htmlFor="globalPreschoolNotes">الملاحظة العامة</Label>
                  <Textarea
                    id="globalPreschoolNotes"
                    value={globalPreschoolNoteText}
                    onChange={(e) => setGlobalPreschoolNoteText(e.target.value)}
                    placeholder="أضف ملاحظة عامة تطبق على جميع خطط الروضة/الحضانة..."
                    className="min-h-[120px]"
                  />
                </div>
                <DialogFooter>
                  <DialogClose asChild><Button variant="outline">إلغاء</Button></DialogClose>
                  <Button onClick={handleSaveGlobalPreschoolNote}><Save className="ml-2 h-4 w-4" /> حفظ الملاحظة</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {globalPreschoolNote && (
              <div className="text-sm p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-green-800">{globalPreschoolNote.notes}</p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
