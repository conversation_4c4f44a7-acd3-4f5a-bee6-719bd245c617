"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Loader2, Bell, Mail, CheckCircle, AlertCircle } from 'lucide-react';
import { useNotificationPreferences } from '@/hooks/useNotificationPreferences';
import { useToast } from '@/hooks/use-toast';

export function NotificationPreferences() {
  const { preferences, isLoading, error, updatePreferences } = useNotificationPreferences();
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);

  const handleToggle = async (field: string, value: boolean) => {
    setIsSaving(true);
    try {
      await updatePreferences({ [field]: value });
      toast({
        title: "تم التحديث",
        description: "تم تحديث تفضيلات الإشعارات بنجاح.",
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تحديث تفضيلات الإشعارات.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleFrequencyChange = async (frequency: 'immediate' | 'daily' | 'weekly') => {
    setIsSaving(true);
    try {
      await updatePreferences({ emailFrequency: frequency });
      toast({
        title: "تم التحديث",
        description: "تم تحديث تكرار الإشعارات بنجاح.",
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تحديث تكرار الإشعارات.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading && !preferences) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <Loader2 className="h-6 w-6 animate-spin ml-2" />
          <span>جاري تحميل تفضيلات الإشعارات...</span>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!preferences) return null;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          <CardTitle>تفضيلات الإشعارات</CardTitle>
        </div>
        <CardDescription>
          تخصيص أنواع الإشعارات وطرق التسليم
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* In-App Notifications */}
        <div>
          <h4 className="text-sm font-medium mb-4 flex items-center gap-2">
            <Bell className="h-4 w-4" />
            الإشعارات داخل التطبيق
          </h4>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between space-x-2 space-x-reverse border p-3 rounded-lg">
              <Label htmlFor="enable-in-app" className="flex flex-col space-y-1 cursor-pointer">
                <span>تفعيل الإشعارات داخل التطبيق</span>
                <span className="font-normal text-xs text-muted-foreground">
                  عرض الإشعارات في واجهة التطبيق
                </span>
              </Label>
              <Switch
                id="enable-in-app"
                checked={preferences.enableInApp}
                onCheckedChange={(checked) => handleToggle('enableInApp', checked)}
                disabled={isSaving}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div className="flex items-center justify-between space-x-2 space-x-reverse border p-3 rounded-lg">
                <Label htmlFor="assessment-reminders" className="cursor-pointer">
                  <span className="text-sm">تذكيرات التقييم</span>
                </Label>
                <Switch
                  id="assessment-reminders"
                  checked={preferences.assessmentReminders}
                  onCheckedChange={(checked) => handleToggle('assessmentReminders', checked)}
                  disabled={isSaving || !preferences.enableInApp}
                />
              </div>

              <div className="flex items-center justify-between space-x-2 space-x-reverse border p-3 rounded-lg">
                <Label htmlFor="service-alerts" className="cursor-pointer">
                  <span className="text-sm">تنبيهات الخدمة</span>
                </Label>
                <Switch
                  id="service-alerts"
                  checked={preferences.serviceAlerts}
                  onCheckedChange={(checked) => handleToggle('serviceAlerts', checked)}
                  disabled={isSaving || !preferences.enableInApp}
                />
              </div>

              <div className="flex items-center justify-between space-x-2 space-x-reverse border p-3 rounded-lg">
                <Label htmlFor="plan-updates" className="cursor-pointer">
                  <span className="text-sm">تحديثات الخطط</span>
                </Label>
                <Switch
                  id="plan-updates"
                  checked={preferences.planUpdates}
                  onCheckedChange={(checked) => handleToggle('planUpdates', checked)}
                  disabled={isSaving || !preferences.enableInApp}
                />
              </div>

              <div className="flex items-center justify-between space-x-2 space-x-reverse border p-3 rounded-lg">
                <Label htmlFor="session-note-alerts" className="cursor-pointer">
                  <span className="text-sm">محاضر الجلسات</span>
                </Label>
                <Switch
                  id="session-note-alerts"
                  checked={preferences.sessionNoteAlerts}
                  onCheckedChange={(checked) => handleToggle('sessionNoteAlerts', checked)}
                  disabled={isSaving || !preferences.enableInApp}
                />
              </div>

              <div className="flex items-center justify-between space-x-2 space-x-reverse border p-3 rounded-lg">
                <Label htmlFor="system-notifications" className="cursor-pointer">
                  <span className="text-sm">إشعارات النظام</span>
                </Label>
                <Switch
                  id="system-notifications"
                  checked={preferences.systemNotifications}
                  onCheckedChange={(checked) => handleToggle('systemNotifications', checked)}
                  disabled={isSaving || !preferences.enableInApp}
                />
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Email Notifications */}
        <div>
          <h4 className="text-sm font-medium mb-4 flex items-center gap-2">
            <Mail className="h-4 w-4" />
            الإشعارات عبر البريد الإلكتروني
          </h4>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between space-x-2 space-x-reverse border p-3 rounded-lg">
              <Label htmlFor="enable-email" className="flex flex-col space-y-1 cursor-pointer">
                <span>تفعيل الإشعارات عبر البريد الإلكتروني</span>
                <span className="font-normal text-xs text-muted-foreground">
                  إرسال الإشعارات إلى بريدك الإلكتروني
                </span>
              </Label>
              <Switch
                id="enable-email"
                checked={preferences.enableEmail}
                onCheckedChange={(checked) => handleToggle('enableEmail', checked)}
                disabled={isSaving}
              />
            </div>

            {preferences.enableEmail && (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="flex items-center justify-between space-x-2 space-x-reverse border p-3 rounded-lg">
                    <Label htmlFor="email-assessment-reminders" className="cursor-pointer">
                      <span className="text-sm">تذكيرات التقييم</span>
                    </Label>
                    <Switch
                      id="email-assessment-reminders"
                      checked={preferences.emailAssessmentReminders}
                      onCheckedChange={(checked) => handleToggle('emailAssessmentReminders', checked)}
                      disabled={isSaving}
                    />
                  </div>

                  <div className="flex items-center justify-between space-x-2 space-x-reverse border p-3 rounded-lg">
                    <Label htmlFor="email-service-alerts" className="cursor-pointer">
                      <span className="text-sm">تنبيهات الخدمة</span>
                    </Label>
                    <Switch
                      id="email-service-alerts"
                      checked={preferences.emailServiceAlerts}
                      onCheckedChange={(checked) => handleToggle('emailServiceAlerts', checked)}
                      disabled={isSaving}
                    />
                  </div>

                  <div className="flex items-center justify-between space-x-2 space-x-reverse border p-3 rounded-lg">
                    <Label htmlFor="email-plan-updates" className="cursor-pointer">
                      <span className="text-sm">تحديثات الخطط</span>
                    </Label>
                    <Switch
                      id="email-plan-updates"
                      checked={preferences.emailPlanUpdates}
                      onCheckedChange={(checked) => handleToggle('emailPlanUpdates', checked)}
                      disabled={isSaving}
                    />
                  </div>

                  <div className="flex items-center justify-between space-x-2 space-x-reverse border p-3 rounded-lg">
                    <Label htmlFor="email-session-note-alerts" className="cursor-pointer">
                      <span className="text-sm">محاضر الجلسات</span>
                    </Label>
                    <Switch
                      id="email-session-note-alerts"
                      checked={preferences.emailSessionNoteAlerts}
                      onCheckedChange={(checked) => handleToggle('emailSessionNoteAlerts', checked)}
                      disabled={isSaving}
                    />
                  </div>

                  <div className="flex items-center justify-between space-x-2 space-x-reverse border p-3 rounded-lg">
                    <Label htmlFor="email-system-notifications" className="cursor-pointer">
                      <span className="text-sm">إشعارات النظام</span>
                    </Label>
                    <Switch
                      id="email-system-notifications"
                      checked={preferences.emailSystemNotifications}
                      onCheckedChange={(checked) => handleToggle('emailSystemNotifications', checked)}
                      disabled={isSaving}
                    />
                  </div>
                </div>

                <div className="border p-3 rounded-lg">
                  <Label htmlFor="email-frequency" className="text-sm font-medium">
                    تكرار الإشعارات عبر البريد الإلكتروني
                  </Label>
                  <Select
                    value={preferences.emailFrequency}
                    onValueChange={handleFrequencyChange}
                    disabled={isSaving}
                  >
                    <SelectTrigger className="mt-2">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="immediate">فوري</SelectItem>
                      <SelectItem value="daily">يومي</SelectItem>
                      <SelectItem value="weekly">أسبوعي</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
