import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

export async function GET() {
  const caseStudyData = await prisma.caseStudyData.findMany();
  return NextResponse.json(caseStudyData);
}

export async function POST(request: Request) {
  const data = await request.json();
  const caseStudyData = await prisma.caseStudyData.create({ data });
  return NextResponse.json(caseStudyData, { status: 201 });
}
