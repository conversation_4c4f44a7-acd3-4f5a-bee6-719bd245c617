import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for child data
const childSchema = z.object({
  childIdNumber: z.string().min(1, 'Child ID is required'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
  birthDate: z.string().datetime('Invalid birth date'),
  enrollmentDate: z.string().datetime('Invalid enrollment date'),
  specialistName: z.string().min(2, 'Specialist name is required'),
  avatarUrl: z.string().url().optional(),
  gender: z.enum(['male', 'female', 'other', 'unknown']).optional(),
  caseStudyNotes: z.string().optional(),
});

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const children = await prisma.child.findMany({
      where: { isDeleted: { not: true } },
      orderBy: { enrollmentDate: 'desc' },
      include: {
        assessments: {
          select: { id: true, assessmentDate: true },
          orderBy: { assessmentDate: 'desc' },
          take: 1,
        },
        _count: {
          select: {
            assessments: true,
            learningPlans: true,
            sessionNotes: true,
          },
        },
      },
    });

    return NextResponse.json(children);
  } catch (error) {
    console.error('Error fetching children:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only certain roles can create children
    const allowedRoles = ['super_admin', 'eiu_manager', 'case_manager', 'specialist'];
    if (!allowedRoles.includes(session.user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = childSchema.parse(body);

    // Check if child ID already exists
    const existingChild = await prisma.child.findUnique({
      where: { childIdNumber: validatedData.childIdNumber },
    });

    if (existingChild) {
      return NextResponse.json(
        { error: 'Child with this ID already exists' },
        { status: 409 }
      );
    }

    const child = await prisma.child.create({
      data: {
        ...validatedData,
        birthDate: new Date(validatedData.birthDate),
        enrollmentDate: new Date(validatedData.enrollmentDate),
      },
      include: {
        _count: {
          select: {
            assessments: true,
            learningPlans: true,
            sessionNotes: true,
          },
        },
      },
    });

    return NextResponse.json(child, { status: 201 });
  } catch (error) {
    console.error('Error creating child:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
