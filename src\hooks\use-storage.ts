import { useState, useEffect, useCallback } from 'react';
import type { Child, Assessment, User, LearningPlan, ComprehensiveReport, PlanNote, CaseStudyData } from '@/lib/types';
import {
  getChildren,
  getChildById,
  saveChild,
  deleteChild,
  getAssessments,
  getAssessmentsByChildId,
  getAssessmentById,
  saveAssessment,
  deleteAssessment,
  getUsers,
  getUserById,
  saveUser,
  getLearningPlans,
  getLearningPlansByChildId,
  saveLearningPlan,
  getComprehensiveReports,
  getComprehensiveReportsByChildId,
  saveComprehensiveReport,
  getComprehensiveReportById,
  deleteComprehensiveReport,
  getPlanNotes,
  getPlanNotesByChildId,
  savePlanNote,
  getCaseStudyData,
  saveCaseStudyData,
} from '@/lib/storage';

// Custom hook for children management
export function useChildren() {
  const [children, setChildren] = useState<Child[]>([]);
  const [loading, setLoading] = useState(true);

  const refreshChildren = useCallback(async () => {
    setLoading(true);
    const children = await getChildren();
    setChildren(children);
    setLoading(false);
  }, []);

  useEffect(() => {
    refreshChildren();
  }, [refreshChildren]);

  const addChild = useCallback(async (child: Child) => {
    const newChild = await saveChild(child);
    refreshChildren();
    return newChild;
  }, [refreshChildren]);

  const updateChild = useCallback(async (child: Child) => {
    const updatedChild = await saveChild(child);
    refreshChildren();
    return updatedChild;
  }, [refreshChildren]);

  const removeChild = useCallback(async (childId: string) => {
    await deleteChild(childId);
    refreshChildren();
  }, [refreshChildren]);

  const getChild = useCallback(async (childId: string) => {
    return getChildById(childId);
  }, []);

  return {
    children,
    loading,
    addChild,
    updateChild,
    removeChild,
    getChild,
    refreshChildren,
  };
}

// Custom hook for assessments management
export function useAssessments(childId?: string) {
  const [assessments, setAssessments] = useState<Assessment[]>([]);
  const [loading, setLoading] = useState(true);

  const refreshAssessments = useCallback(async () => {
    setLoading(true);
    if (childId) {
      const assessments = await getAssessmentsByChildId(childId);
      setAssessments(assessments);
    } else {
      const assessments = await getAssessments();
      setAssessments(assessments);
    }
    setLoading(false);
  }, [childId]);

  useEffect(() => {
    refreshAssessments();
  }, [refreshAssessments]);

  const addAssessment = useCallback(async (assessment: Assessment) => {
    const newAssessment = await saveAssessment(assessment);
    refreshAssessments();
    return newAssessment;
  }, [refreshAssessments]);

  const updateAssessment = useCallback(async (assessment: Assessment) => {
    const updatedAssessment = await saveAssessment(assessment);
    refreshAssessments();
    return updatedAssessment;
  }, [refreshAssessments]);

  const removeAssessment = useCallback(async (assessmentId: string) => {
    await deleteAssessment(assessmentId);
    refreshAssessments();
  }, [refreshAssessments]);

  const getAssessment = useCallback(async (assessmentId: string) => {
    return getAssessmentById(assessmentId);
  }, []);

  return {
    assessments,
    loading,
    addAssessment,
    updateAssessment,
    removeAssessment,
    getAssessment,
    refreshAssessments,
  };
}

// Custom hook for users management
export function useUsers() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);

  const refreshUsers = useCallback(async () => {
    setLoading(true);
    const users = await getUsers();
    setUsers(users);
    setLoading(false);
  }, []);

  useEffect(() => {
    refreshUsers();
  }, [refreshUsers]);

  const addUser = useCallback(async (user: User) => {
    const newUser = await saveUser(user);
    refreshUsers();
    return newUser;
  }, [refreshUsers]);

  const updateUser = useCallback(async (user: User) => {
    const updatedUser = await saveUser(user);
    refreshUsers();
    return updatedUser;
  }, [refreshUsers]);

  const getUser = useCallback(async (userId: string) => {
    return getUserById(userId);
  }, []);

  return {
    users,
    loading,
    addUser,
    updateUser,
    getUser,
    refreshUsers,
  };
}

// Custom hook for learning plans management
export function useLearningPlans(childId?: string) {
  const [learningPlans, setLearningPlans] = useState<LearningPlan[]>([]);
  const [loading, setLoading] = useState(true);

  const refreshLearningPlans = useCallback(async () => {
    setLoading(true);
    if (childId) {
      const plans = await getLearningPlansByChildId(childId);
      setLearningPlans(plans);
    } else {
      const plans = await getLearningPlans();
      setLearningPlans(plans);
    }
    setLoading(false);
  }, [childId]);

  useEffect(() => {
    refreshLearningPlans();
  }, [refreshLearningPlans]);

  const addLearningPlan = useCallback(async (plan: LearningPlan) => {
    const newPlan = await saveLearningPlan(plan);
    refreshLearningPlans();
    return newPlan;
  }, [refreshLearningPlans]);

  const updateLearningPlan = useCallback(async (plan: LearningPlan) => {
    const updatedPlan = await saveLearningPlan(plan);
    refreshLearningPlans();
    return updatedPlan;
  }, [refreshLearningPlans]);

  return {
    learningPlans,
    loading,
    addLearningPlan,
    updateLearningPlan,
    refreshLearningPlans,
  };
}

// Custom hook for comprehensive reports management
export function useComprehensiveReports(childId?: string) {
  const [comprehensiveReports, setComprehensiveReports] = useState<ComprehensiveReport[]>([]);
  const [loading, setLoading] = useState(true);

  const refreshComprehensiveReports = useCallback(async () => {
    setLoading(true);
    if (childId) {
      const reports = await getComprehensiveReportsByChildId(childId);
      setComprehensiveReports(reports);
    } else {
      const reports = await getComprehensiveReports();
      setComprehensiveReports(reports);
    }
    setLoading(false);
  }, [childId]);

  useEffect(() => {
    refreshComprehensiveReports();
  }, [refreshComprehensiveReports]);

  const addComprehensiveReport = useCallback(async (report: ComprehensiveReport) => {
    const newReport = await saveComprehensiveReport(report);
    refreshComprehensiveReports();
    return newReport;
  }, [refreshComprehensiveReports]);

  const updateComprehensiveReport = useCallback(async (report: ComprehensiveReport) => {
    const updatedReport = await saveComprehensiveReport(report);
    refreshComprehensiveReports();
    return updatedReport;
  }, [refreshComprehensiveReports]);

  const removeComprehensiveReport = useCallback(async (reportId: string) => {
    await deleteComprehensiveReport(reportId);
    refreshComprehensiveReports();
  }, [refreshComprehensiveReports]);

  const getComprehensiveReport = useCallback(async (reportId: string) => {
    return getComprehensiveReportById(reportId);
  }, []);

  return {
    comprehensiveReports,
    loading,
    addComprehensiveReport,
    updateComprehensiveReport,
    removeComprehensiveReport,
    getComprehensiveReport,
    refreshComprehensiveReports,
  };
}

// Custom hook for plan notes management
export function usePlanNotes(childId?: string) {
  const [planNotes, setPlanNotes] = useState<PlanNote[]>([]);
  const [loading, setLoading] = useState(true);

  const refreshPlanNotes = useCallback(async () => {
    setLoading(true);
    if (childId) {
      const notes = await getPlanNotesByChildId(childId);
      setPlanNotes(notes);
    } else {
      const notes = await getPlanNotes();
      setPlanNotes(notes);
    }
    setLoading(false);
  }, [childId]);

  useEffect(() => {
    refreshPlanNotes();
  }, [refreshPlanNotes]);

  const addPlanNote = useCallback(async (planNote: PlanNote) => {
    const newNote = await savePlanNote(planNote);
    refreshPlanNotes();
    return newNote;
  }, [refreshPlanNotes]);

  const updatePlanNote = useCallback(async (planNote: PlanNote) => {
    const updatedNote = await savePlanNote(planNote);
    refreshPlanNotes();
    return updatedNote;
  }, [refreshPlanNotes]);

  return {
    planNotes,
    loading,
    addPlanNote,
    updatePlanNote,
    refreshPlanNotes,
  };
}

// Custom hook for case study data management
export function useCaseStudyData(childId?: string) {
  const [caseStudyData, setCaseStudyData] = useState<CaseStudyData | null>(null);
  const [loading, setLoading] = useState(true);

  const refreshCaseStudyData = useCallback(async () => {
    setLoading(true);
    if (childId) {
      const data = await getCaseStudyData(childId);
      setCaseStudyData(data);
    }
    setLoading(false);
  }, [childId]);

  useEffect(() => {
    refreshCaseStudyData();
  }, [refreshCaseStudyData]);

  const updateCaseStudyData = useCallback(async (caseStudy: CaseStudyData) => {
    if (childId) {
      const updatedChild = await saveCaseStudyData(childId, caseStudy);
      refreshCaseStudyData();
      return updatedChild;
    }
  }, [childId, refreshCaseStudyData]);

  return {
    caseStudyData,
    loading,
    updateCaseStudyData,
    refreshCaseStudyData,
  };
}
