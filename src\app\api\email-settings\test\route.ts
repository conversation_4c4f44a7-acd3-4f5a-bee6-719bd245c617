import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';
import { sendTestEmail } from '@/lib/email';

// POST /api/email-settings/test - Send a test email (admin only)
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true, email: true, name: true },
    });

    if (user?.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 });
    }

    if (!user.email) {
      return NextResponse.json({ error: 'User email not found' }, { status: 400 });
    }

    const body = await request.json();
    const { testEmail } = body;

    const emailToSend = testEmail || user.email;

    try {
      await sendTestEmail(emailToSend, user.name || 'Admin');
      
      // Update the test email sent timestamp
      await prisma.emailSettings.upsert({
        where: { id: 'default' },
        update: { testEmailSent: new Date() },
        create: {
          id: 'default',
          smtpPort: 587,
          smtpSecure: false,
          isEnabled: false,
          testEmailSent: new Date(),
        },
      });

      return NextResponse.json({
        message: 'Test email sent successfully',
        sentTo: emailToSend,
      });
    } catch (emailError) {
      console.error('Error sending test email:', emailError);
      return NextResponse.json(
        { error: 'Failed to send test email. Please check your email settings.' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error in test email endpoint:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
