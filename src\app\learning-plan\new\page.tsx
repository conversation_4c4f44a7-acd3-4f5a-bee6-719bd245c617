"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  GraduationCap, 
  User, 
  Calendar,
  CheckCircle,
  AlertCircle,
  ArrowLeft,
  ClipboardList
} from 'lucide-react';
import { useChildren, useAssessments } from '@/hooks/use-storage';
import { calculateAge, formatDate } from '@/lib/utils';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import type { Child, Assessment } from '@/lib/types';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface ChildWithLatestAssessment extends Child {
  latestAssessment: Assessment | null;
  assessmentCount: number;
}

export default function NewLearningPlanPage() {
  const { children, loading: childrenLoading } = useChildren();
  const { assessments, loading: assessmentsLoading } = useAssessments();
  const [selectedChildId, setSelectedChildId] = useState<string>('');
  const router = useRouter();

  const loading = childrenLoading || assessmentsLoading;

  // Get children with their latest assessments
  const childrenWithAssessments: ChildWithLatestAssessment[] = React.useMemo(() => {
    return children.map(child => {
      const childAssessments = assessments.filter(assessment => assessment.childId === child.id);
      const latestAssessment = childAssessments.length > 0 
        ? childAssessments.sort((a, b) => new Date(b.assessmentDate).getTime() - new Date(a.assessmentDate).getTime())[0]
        : null;

      return {
        ...child,
        latestAssessment,
        assessmentCount: childAssessments.length
      };
    });
  }, [children, assessments]);

  // Filter children who have assessments
  const childrenWithAssessmentsAvailable = childrenWithAssessments.filter(child => child.latestAssessment);

  const selectedChild = childrenWithAssessmentsAvailable.find(child => child.id === selectedChildId);

  const handleCreatePlan = () => {
    if (selectedChildId) {
      router.push(`/children/${selectedChildId}/plan`);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري تحميل بيانات الأطفال...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/learning-plan">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div className="space-y-1">
          <h1 className="text-3xl font-bold text-primary flex items-center gap-3">
            <GraduationCap className="h-8 w-8" />
            إنشاء خطة تعليمية جديدة
          </h1>
          <p className="text-muted-foreground">
            اختر الطفل لإنشاء خطة تعليمية مخصصة بناءً على تقييمه
          </p>
        </div>
      </div>

      {/* Instructions */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          يمكن إنشاء خطة تعليمية فقط للأطفال الذين لديهم تقييم سابق. إذا لم يكن لدى الطفل تقييم، يرجى إجراء تقييم أولاً.
        </AlertDescription>
      </Alert>

      {/* Child Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            اختيار الطفل
          </CardTitle>
          <CardDescription>
            اختر الطفل الذي تريد إنشاء خطة تعليمية له من القائمة أدناه
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {childrenWithAssessmentsAvailable.length > 0 ? (
            <>
              <div className="space-y-2">
                <label className="text-sm font-medium">اختر طفل من القائمة:</label>
                <Select value={selectedChildId} onValueChange={setSelectedChildId}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="اختر طفل..." />
                  </SelectTrigger>
                  <SelectContent>
                    {childrenWithAssessmentsAvailable.map(child => (
                      <SelectItem key={child.id} value={child.id}>
                        <div className="flex items-center gap-2">
                          <span>{child.name}</span>
                          <Badge variant="outline" className="text-xs">
                            آخر تقييم: {formatDate(child.latestAssessment!.assessmentDate)}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Selected Child Info */}
              {selectedChild && (
                <Card className="border-2 border-primary/20 bg-primary/5">
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-green-500" />
                      الطفل المختار
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-4">
                      <Avatar className="h-16 w-16">
                        <AvatarImage src={selectedChild.avatarUrl} />
                        <AvatarFallback className="text-lg">
                          {selectedChild.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div className="space-y-2">
                        <h3 className="text-xl font-semibold">{selectedChild.name}</h3>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            العمر: {calculateAge(selectedChild.birthDate).years} سنة و {calculateAge(selectedChild.birthDate).months} شهر
                          </div>
                          <div className="flex items-center gap-1">
                            <User className="h-4 w-4" />
                            الأخصائي: {selectedChild.specialistName}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary">
                            رقم الطفل: {selectedChild.childIdNumber}
                          </Badge>
                          {selectedChild.gender && (
                            <Badge variant="outline">
                              {selectedChild.gender === 'male' ? 'ذكر' : 'أنثى'}
                            </Badge>
                          )}
                        </div>
                        
                        {/* Assessment Info */}
                        <div className="pt-2">
                          <h4 className="font-medium mb-2">معلومات التقييم:</h4>
                          <div className="space-y-1 text-sm">
                            <div className="flex items-center gap-2">
                              <ClipboardList className="h-4 w-4" />
                              <span>آخر تقييم: {formatDate(selectedChild.latestAssessment!.assessmentDate)}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <span>عدد المهارات المقيمة: {selectedChild.latestAssessment!.assessedSkills.length}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <span>المهارات المتقنة: {selectedChild.latestAssessment!.assessedSkills.filter(s => s.status === 'yes').length}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <span>إجمالي التقييمات: {selectedChild.assessmentCount}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Action Button */}
              <div className="flex justify-center pt-4">
                <Button 
                  onClick={handleCreatePlan}
                  disabled={!selectedChildId}
                  size="lg"
                  className="px-8"
                >
                  <GraduationCap className="h-5 w-5 mr-2" />
                  إنشاء الخطة التعليمية
                </Button>
              </div>
            </>
          ) : (
            <div className="text-center py-8">
              <ClipboardList className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-xl font-semibold">لا يوجد أطفال مؤهلون</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                لإنشاء خطة تعليمية، يجب أن يكون لدى الطفل تقييم سابق على الأقل
              </p>
              <div className="mt-4 space-y-2">
                <Link href="/assessment/new" className="inline-block">
                  <Button>
                    <ClipboardList className="h-4 w-4 mr-2" />
                    إجراء تقييم جديد
                  </Button>
                </Link>
                <p className="text-xs text-muted-foreground">
                  أو
                </p>
                <Link href="/children" className="inline-block">
                  <Button variant="outline">
                    <User className="h-4 w-4 mr-2" />
                    إضافة طفل جديد
                  </Button>
                </Link>
              </div>
            </div>
          )}

          {/* Children without assessments warning */}
          {children.length > childrenWithAssessmentsAvailable.length && (
            <Alert className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                هناك {children.length - childrenWithAssessmentsAvailable.length} طفل/أطفال لا يملكون تقييمات.
                يرجى إجراء تقييم لهم أولاً قبل إنشاء خطط تعليمية.
                <Link href="/assessment/new" className="ml-2 underline">
                  إجراء تقييم جديد
                </Link>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Process Steps */}
      <Card>
        <CardHeader>
          <CardTitle>خطوات إنشاء الخطة التعليمية</CardTitle>
          <CardDescription>
            نظرة عامة على عملية إنشاء الخطة التعليمية
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="flex items-start gap-3 p-4 rounded-lg border">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-white text-sm font-medium">
                1
              </div>
              <div className="space-y-1">
                <h4 className="font-medium">اختيار الطفل</h4>
                <p className="text-sm text-muted-foreground">
                  حدد الطفل المراد إنشاء خطة له من القائمة
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-4 rounded-lg border">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-white text-sm font-medium">
                2
              </div>
              <div className="space-y-1">
                <h4 className="font-medium">مراجعة التقييم</h4>
                <p className="text-sm text-muted-foreground">
                  استعراض نتائج التقييم الأخير للطفل
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-4 rounded-lg border">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-white text-sm font-medium">
                3
              </div>
              <div className="space-y-1">
                <h4 className="font-medium">إنشاء الخطة</h4>
                <p className="text-sm text-muted-foreground">
                  توليد خطة تعليمية مخصصة وحفظها
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 