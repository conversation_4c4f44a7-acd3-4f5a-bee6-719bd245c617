import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for learning plan updates
const updateLearningPlanSchema = z.object({
  planDetails: z.string().min(1, 'Plan details are required').optional(),
  suggestedDailyGoals: z.string().optional(),
});

export async function GET(
  request: Request,
  { params }: { params: { planId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const learningPlan = await prisma.learningPlan.findUnique({
      where: { id: params.planId },
      include: {
        child: {
          select: {
            id: true,
            name: true,
            childIdNumber: true,
          },
        },
        assessment: {
          select: {
            id: true,
            assessmentDate: true,
          },
        },
      },
    });

    if (!learningPlan) {
      return NextResponse.json(
        { error: 'Learning plan not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(learningPlan);
  } catch (error) {
    console.error('Error fetching learning plan:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { planId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only certain roles can update learning plans
    const allowedRoles = ['super_admin', 'eiu_manager', 'case_manager', 'specialist'];
    if (!allowedRoles.includes(session.user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = updateLearningPlanSchema.parse(body);

    const learningPlan = await prisma.learningPlan.update({
      where: { id: params.planId },
      data: validatedData,
      include: {
        child: {
          select: {
            id: true,
            name: true,
            childIdNumber: true,
          },
        },
        assessment: {
          select: {
            id: true,
            assessmentDate: true,
          },
        },
      },
    });

    return NextResponse.json(learningPlan);
  } catch (error) {
    console.error('Error updating learning plan:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { planId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only certain roles can delete learning plans
    const allowedRoles = ['super_admin', 'eiu_manager'];
    if (!allowedRoles.includes(session.user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    await prisma.learningPlan.delete({
      where: { id: params.planId },
    });

    return new Response(null, { status: 204 });
  } catch (error) {
    console.error('Error deleting learning plan:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
