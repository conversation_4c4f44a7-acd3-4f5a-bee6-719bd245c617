import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

export async function GET(
  request: Request,
  { params }: { params: { planId: string } }
) {
  const learningPlan = await prisma.learningPlan.findUnique({
    where: { id: params.planId },
  });
  return NextResponse.json(learningPlan);
}

export async function PUT(
  request: Request,
  { params }: { params: { planId: string } }
) {
  const data = await request.json();
  const learningPlan = await prisma.learningPlan.update({
    where: { id: params.planId },
    data,
  });
  return NextResponse.json(learningPlan);
}

export async function DELETE(
  request: Request,
  { params }: { params: { planId: string } }
) {
  await prisma.learningPlan.delete({
    where: { id: params.planId },
  });
  return new Response(null, { status: 204 });
}
