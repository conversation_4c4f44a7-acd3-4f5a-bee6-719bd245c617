import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

export async function GET(
  request: Request,
  { params }: { params: { reportId: string } }
) {
  const comprehensiveReport = await prisma.comprehensiveReport.findUnique({
    where: { id: params.reportId },
  });
  return NextResponse.json(comprehensiveReport);
}

export async function PUT(
  request: Request,
  { params }: { params: { reportId: string } }
) {
  const data = await request.json();
  const comprehensiveReport = await prisma.comprehensiveReport.update({
    where: { id: params.reportId },
    data,
  });
  return NextResponse.json(comprehensiveReport);
}

export async function DELETE(
  request: Request,
  { params }: { params: { reportId: string } }
) {
  await prisma.comprehensiveReport.delete({
    where: { id: params.reportId },
  });
  return new Response(null, { status: 204 });
}
