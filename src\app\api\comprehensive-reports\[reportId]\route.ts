import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for comprehensive report updates
const updateComprehensiveReportSchema = z.object({
  additionalFocus: z.string().optional(),
  executiveSummary: z.string().min(1, 'Executive summary is required').optional(),
  strengths: z.array(z.string()).min(1, 'At least one strength is required').optional(),
  areasForDevelopment: z.array(z.string()).min(1, 'At least one area for development is required').optional(),
  dataAnalysisHighlights: z.array(z.string()).min(1, 'At least one data analysis highlight is required').optional(),
  actionableRecommendations: z.array(z.string()).min(1, 'At least one recommendation is required').optional(),
});

export async function GET(
  request: Request,
  { params }: { params: { reportId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const comprehensiveReport = await prisma.comprehensiveReport.findUnique({
      where: { id: params.reportId },
      include: {
        child: {
          select: {
            id: true,
            name: true,
            childIdNumber: true,
          },
        },
        assessment: {
          select: {
            id: true,
            assessmentDate: true,
          },
        },
      },
    });

    if (!comprehensiveReport) {
      return NextResponse.json(
        { error: 'Comprehensive report not found' },
        { status: 404 }
      );
    }

    // Parse JSON fields back to arrays
    const reportWithParsedArrays = {
      ...comprehensiveReport,
      strengths: JSON.parse(comprehensiveReport.strengths),
      areasForDevelopment: JSON.parse(comprehensiveReport.areasForDevelopment),
      dataAnalysisHighlights: JSON.parse(comprehensiveReport.dataAnalysisHighlights),
      actionableRecommendations: JSON.parse(comprehensiveReport.actionableRecommendations),
    };

    return NextResponse.json(reportWithParsedArrays);
  } catch (error) {
    console.error('Error fetching comprehensive report:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { reportId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only certain roles can update comprehensive reports
    const allowedRoles = ['super_admin', 'eiu_manager', 'case_manager', 'specialist'];
    if (!allowedRoles.includes(session.user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = updateComprehensiveReportSchema.parse(body);

    // Prepare update data, converting arrays to JSON strings
    const updateData: any = {};
    if (validatedData.additionalFocus !== undefined) {
      updateData.additionalFocus = validatedData.additionalFocus;
    }
    if (validatedData.executiveSummary !== undefined) {
      updateData.executiveSummary = validatedData.executiveSummary;
    }
    if (validatedData.strengths !== undefined) {
      updateData.strengths = JSON.stringify(validatedData.strengths);
    }
    if (validatedData.areasForDevelopment !== undefined) {
      updateData.areasForDevelopment = JSON.stringify(validatedData.areasForDevelopment);
    }
    if (validatedData.dataAnalysisHighlights !== undefined) {
      updateData.dataAnalysisHighlights = JSON.stringify(validatedData.dataAnalysisHighlights);
    }
    if (validatedData.actionableRecommendations !== undefined) {
      updateData.actionableRecommendations = JSON.stringify(validatedData.actionableRecommendations);
    }

    const comprehensiveReport = await prisma.comprehensiveReport.update({
      where: { id: params.reportId },
      data: updateData,
      include: {
        child: {
          select: {
            id: true,
            name: true,
            childIdNumber: true,
          },
        },
        assessment: {
          select: {
            id: true,
            assessmentDate: true,
          },
        },
      },
    });

    // Parse JSON fields back to arrays
    const reportWithParsedArrays = {
      ...comprehensiveReport,
      strengths: JSON.parse(comprehensiveReport.strengths),
      areasForDevelopment: JSON.parse(comprehensiveReport.areasForDevelopment),
      dataAnalysisHighlights: JSON.parse(comprehensiveReport.dataAnalysisHighlights),
      actionableRecommendations: JSON.parse(comprehensiveReport.actionableRecommendations),
    };

    return NextResponse.json(reportWithParsedArrays);
  } catch (error) {
    console.error('Error updating comprehensive report:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { reportId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only certain roles can delete comprehensive reports
    const allowedRoles = ['super_admin', 'eiu_manager'];
    if (!allowedRoles.includes(session.user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    await prisma.comprehensiveReport.delete({
      where: { id: params.reportId },
    });

    return new Response(null, { status: 204 });
  } catch (error) {
    console.error('Error deleting comprehensive report:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
