"use client";

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { EmailSettings } from '@/lib/types';

export function useEmailSettings() {
  const { data: session } = useSession();
  const [settings, setSettings] = useState<EmailSettings | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch email settings
  const fetchSettings = useCallback(async () => {
    if (!session?.user?.id) return;

    // Don't fetch if user is not admin
    if (session?.user?.role !== 'admin') {
      setError('Access denied - Admin privileges required');
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/email-settings');

      if (!response.ok) {
        if (response.status === 403) {
          throw new Error('Access denied - Admin privileges required');
        }
        throw new Error('Failed to fetch email settings');
      }

      const data = await response.json();
      setSettings(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error fetching email settings:', err);
    } finally {
      setIsLoading(false);
    }
  }, [session?.user?.id, session?.user?.role]);

  // Update email settings
  const updateSettings = useCallback(async (updatedSettings: Partial<EmailSettings>) => {
    if (!session?.user?.id) return;

    // Don't allow updates if user is not admin
    if (session?.user?.role !== 'admin') {
      const error = new Error('Access denied - Admin privileges required');
      setError(error.message);
      throw error;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/email-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedSettings),
      });

      if (!response.ok) {
        if (response.status === 403) {
          throw new Error('Access denied - Admin privileges required');
        }
        throw new Error('Failed to update email settings');
      }

      const data = await response.json();
      setSettings(data);
      return data;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error updating email settings:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [session?.user?.id, session?.user?.role]);

  // Send test email
  const sendTestEmail = useCallback(async (testEmail?: string) => {
    if (!session?.user?.id) return;

    // Don't allow test email if user is not admin
    if (session?.user?.role !== 'admin') {
      const error = new Error('Access denied - Admin privileges required');
      setError(error.message);
      throw error;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/email-settings/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ testEmail }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send test email');
      }

      const data = await response.json();

      // Refresh settings to update testEmailSent timestamp
      await fetchSettings();

      return data;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error sending test email:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [session?.user?.id, session?.user?.role, fetchSettings]);

  // Initial fetch
  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  return {
    settings,
    isLoading,
    error,
    fetchSettings,
    updateSettings,
    sendTestEmail,
  };
}
