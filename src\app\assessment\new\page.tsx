"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  ClipboardList, 
  User, 
  Calendar,
  CheckCircle,
  AlertCircle,
  ArrowLeft
} from 'lucide-react';
import { useChildren } from '@/hooks/use-storage';
import { calculateAge } from '@/lib/utils';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import type { Child } from '@/lib/types';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

export default function NewAssessmentPage() {
  const { children, loading } = useChildren();
  const [selectedChildId, setSelectedChildId] = useState<string>('');
  const router = useRouter();

  const selectedChild = children.find(child => child.id === selectedChildId);

  const handleCreateAssessment = () => {
    if (selectedChildId) {
      router.push(`/children/${selectedChildId}/assessment/new`);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري تحميل بيانات الأطفال...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/assessment">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div className="space-y-1">
          <h1 className="text-3xl font-bold text-primary flex items-center gap-3">
            <ClipboardList className="h-8 w-8" />
            إنشاء تقييم جديد
          </h1>
          <p className="text-muted-foreground">
            اختر الطفل لبدء عملية التقييم الشاملة
          </p>
        </div>
      </div>

      {/* Instructions */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          يرجى اختيار الطفل الذي تريد إنشاء تقييم له، ثم ستنتقل إلى صفحة التقييم التفصيلية.
        </AlertDescription>
      </Alert>

      {/* Child Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            اختيار الطفل
          </CardTitle>
          <CardDescription>
            اختر الطفل الذي تريد إجراء تقييم له من القائمة أدناه
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {children.length > 0 ? (
            <>
              <div className="space-y-2">
                <label className="text-sm font-medium">اختر طفل من القائمة:</label>
                <Select value={selectedChildId} onValueChange={setSelectedChildId}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="اختر طفل..." />
                  </SelectTrigger>
                  <SelectContent>
                    {children.map(child => (
                      <SelectItem key={child.id} value={child.id}>
                        <div className="flex items-center gap-2">
                          <span>{child.name}</span>
                          <Badge variant="outline" className="text-xs">
                            {calculateAge(child.birthDate).years} سنة
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Selected Child Info */}
              {selectedChild && (
                <Card className="border-2 border-primary/20 bg-primary/5">
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-green-500" />
                      الطفل المختار
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-4">
                      <Avatar className="h-16 w-16">
                        <AvatarImage src={selectedChild.avatarUrl} />
                        <AvatarFallback className="text-lg">
                          {selectedChild.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div className="space-y-1">
                        <h3 className="text-xl font-semibold">{selectedChild.name}</h3>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            العمر: {calculateAge(selectedChild.birthDate).years} سنة و {calculateAge(selectedChild.birthDate).months} شهر
                          </div>
                          <div className="flex items-center gap-1">
                            <User className="h-4 w-4" />
                            الأخصائي: {selectedChild.specialistName}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary">
                            رقم الطفل: {selectedChild.childIdNumber}
                          </Badge>
                          {selectedChild.gender && (
                            <Badge variant="outline">
                              {selectedChild.gender === 'male' ? 'ذكر' : 'أنثى'}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Action Button */}
              <div className="flex justify-center pt-4">
                <Button 
                  onClick={handleCreateAssessment}
                  disabled={!selectedChildId}
                  size="lg"
                  className="px-8"
                >
                  <ClipboardList className="h-5 w-5 mr-2" />
                  بدء التقييم
                </Button>
              </div>
            </>
          ) : (
            <div className="text-center py-8">
              <User className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-xl font-semibold">لا يوجد أطفال مسجلون</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                يجب إضافة طفل أولاً قبل إنشاء تقييم
              </p>
              <Link href="/children" className="mt-4 inline-block">
                <Button>
                  <User className="h-4 w-4 mr-2" />
                  إضافة طفل جديد
                </Button>
              </Link>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Process Steps */}
      <Card>
        <CardHeader>
          <CardTitle>خطوات التقييم</CardTitle>
          <CardDescription>
            نظرة عامة على عملية التقييم التي ستتبعها
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="flex items-start gap-3 p-4 rounded-lg border">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-white text-sm font-medium">
                1
              </div>
              <div className="space-y-1">
                <h4 className="font-medium">اختيار الطفل</h4>
                <p className="text-sm text-muted-foreground">
                  حدد الطفل المراد تقييمه من القائمة
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-4 rounded-lg border">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-white text-sm font-medium">
                2
              </div>
              <div className="space-y-1">
                <h4 className="font-medium">إجراء التقييم</h4>
                <p className="text-sm text-muted-foreground">
                  قيّم المهارات عبر الأبعاد المختلفة
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-4 rounded-lg border">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-white text-sm font-medium">
                3
              </div>
              <div className="space-y-1">
                <h4 className="font-medium">حفظ النتائج</h4>
                <p className="text-sm text-muted-foreground">
                  احفظ التقييم واستعرض النتائج
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 