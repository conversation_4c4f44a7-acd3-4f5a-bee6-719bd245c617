import { genkit } from 'genkit';
import { googleAI } from '@genkit-ai/googleai';

// List of Gemini API keys
const geminiApiKeys = [
  'AIzaSyAfpb_TE6XD-lhVeRz8DbgYLxLZmNY_J2Q',
  'AIzaSyDDJkzJD5t3xpkNsCXLVuYiGO8ny1eWJGg',
  'AIzaSyCvT9HJyvc2z_bsD6k4aDQuHLUQ9M7yeZ4',
  'AIzaSyAPfk2nkp3S2wr-C5_dcsoOaHlfaXg85rs',
  'AIzaSyDAmoD7bsApKkqv_BXpDewXvETJDot7wVc',
  'AIzaSyDhp1sQOYABBjo3FL1dpBMcFrvm0FkTeTE',

];

const randomKey = geminiApiKeys[Math.floor(Math.random() * geminiApiKeys.length)];

export const ai = genkit({
  plugins: [googleAI({
    apiKey: randomKey,
  })],
  model: 'googleai/gemini-2.5-flash',
});
