"use client";

import { <PERSON><PERSON><PERSON><PERSON>, Radial<PERSON>ar<PERSON><PERSON> } from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
  type ChartConfig
} from "@/components/ui/chart";

interface ProgressOverviewChartProps {
  mastered: number;
  implemented: number;
  pending: number;
}

export default function ProgressOverviewChart({ mastered, implemented, pending }: ProgressOverviewChartProps) {
  const total = mastered + implemented + pending;
  
  if (total === 0) {
    return (
      <div className="flex items-center justify-center h-[300px] w-full">
        <div className="text-center">
          <div className="text-muted-foreground mb-2">📊</div>
          <p className="text-sm text-muted-foreground">لا توجد بيانات للعرض</p>
          <p className="text-xs text-muted-foreground mt-1">ابدأ بإضافة تقييمات للأطفال</p>
        </div>
      </div>
    );
  }

  const chartData = [
    { 
      name: 'أهداف معلقة', 
      value: pending, 
      fill: 'hsl(var(--chart-4))',
      percentage: Math.round((pending / total) * 100)
    },
    { 
      name: 'أهداف قيد التنفيذ', 
      value: implemented, 
      fill: 'hsl(var(--chart-3))',
      percentage: Math.round((implemented / total) * 100)
    },
    { 
      name: 'أهداف متقنة', 
      value: mastered, 
      fill: 'hsl(var(--chart-2))',
      percentage: Math.round((mastered / total) * 100)
    },
  ].filter(item => item.value > 0);

  // If after filtering we have no data, show empty state
  if (chartData.length === 0) {
    return (
      <div className="flex items-center justify-center h-[300px] w-full">
        <div className="text-center">
          <div className="text-muted-foreground mb-2">📊</div>
          <p className="text-sm text-muted-foreground">لا توجد بيانات للعرض</p>
        </div>
      </div>
    );
  }

  const chartConfig = chartData.reduce((acc, item) => {
    acc[item.name] = { label: item.name, color: item.fill };
    return acc;
  }, {} as ChartConfig);

  return (
    <div className="h-[300px] w-full">
      <ChartContainer config={chartConfig} className="w-full h-full">
        <RadialBarChart
          cx="50%"
          cy="50%"
          innerRadius="30%"
          outerRadius="90%"
          data={chartData}
          startAngle={90}
          endAngle={450}
        >
          <ChartTooltip 
            content={<ChartTooltipContent nameKey="name" hideLabel />} 
          />
          <RadialBar 
            dataKey="value" 
            cornerRadius={8}
            label={({ cx, cy, midAngle, innerRadius, outerRadius, percentage }) => {
              // Validate all required values to prevent NaN
              if (
                typeof cx !== 'number' || isNaN(cx) ||
                typeof cy !== 'number' || isNaN(cy) ||
                typeof midAngle !== 'number' || isNaN(midAngle) ||
                typeof innerRadius !== 'number' || isNaN(innerRadius) ||
                typeof outerRadius !== 'number' || isNaN(outerRadius) ||
                typeof percentage !== 'number' || isNaN(percentage)
              ) {
                return null; // Don't render label if any value is invalid
              }

              const RADIAN = Math.PI / 180;
              const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
              const x = cx + radius * Math.cos(-midAngle * RADIAN);
              const y = cy + radius * Math.sin(-midAngle * RADIAN);
              
              // Final validation for calculated x and y
              if (isNaN(x) || isNaN(y)) {
                return null;
              }
              
              return (
                <text
                  x={x}
                  y={y}
                  fill="white"
                  textAnchor={x > cx ? 'start' : 'end'}
                  dominantBaseline="central"
                  fontSize="11px"
                  fontWeight="600"
                >
                  {`${percentage}%`}
                </text>
              );
            }}
          />
          <ChartLegend
            content={<ChartLegendContent nameKey="name" />}
            verticalAlign="bottom"
            align="center"
          />
        </RadialBarChart>
      </ChartContainer>
    </div>
  );
}
