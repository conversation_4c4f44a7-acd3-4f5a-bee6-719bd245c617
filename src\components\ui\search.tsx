import * as React from "react"
import { Search as SearchIcon } from "lucide-react"
import { Command, CommandInput, CommandList, CommandEmpty, CommandGroup, CommandItem } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface SearchItem {
  id: string
  label: string
  value: string
  group?: string
}

interface SearchProps {
  items?: SearchItem[]
  placeholder?: string
  emptyMessage?: string
  onSelect?: (value: string) => void
  className?: string
  value?: string
  onValueChange?: (value: string) => void
  showPopover?: boolean
}

const Search = React.forwardRef<HTMLDivElement, SearchProps>(({
  items = [],
  placeholder = "البحث...",
  emptyMessage = "لا توجد نتائج.",
  onSelect,
  className,
  value,
  onValueChange,
  showPopover = false,
  ...props
}, ref) => {
  const [open, setOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState(value || "")

  React.useEffect(() => {
    if (value !== undefined) {
      setSearchValue(value)
    }
  }, [value])

  const handleValueChange = (newValue: string) => {
    setSearchValue(newValue)
    onValueChange?.(newValue)
  }

  const handleSelect = (selectedValue: string) => {
    onSelect?.(selectedValue)
    setOpen(false)
  }

  const groupedItems = React.useMemo(() => {
    const groups: Record<string, SearchItem[]> = {}
    items.forEach(item => {
      const group = item.group || "العام"
      if (!groups[group]) groups[group] = []
      groups[group].push(item)
    })
    return groups
  }, [items])

  if (showPopover && items.length > 0) {
    return (
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn("w-full justify-between text-right", className)}
          >
            <SearchIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            {searchValue || placeholder}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput 
              placeholder={placeholder} 
              value={searchValue}
              onValueChange={handleValueChange}
            />
            <CommandList>
              <CommandEmpty>{emptyMessage}</CommandEmpty>
              {Object.entries(groupedItems).map(([groupName, groupItems]) => (
                <CommandGroup key={groupName} heading={groupName}>
                  {groupItems.map((item) => (
                    <CommandItem
                      key={item.id}
                      onSelect={() => handleSelect(item.value)}
                    >
                      {item.label}
                    </CommandItem>
                  ))}
                </CommandGroup>
              ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    )
  }

  return (
    <div ref={ref} className={cn("relative", className)} {...props}>
      <Command className="rounded-lg border shadow-md">
        <CommandInput 
          placeholder={placeholder}
          value={searchValue}
          onValueChange={handleValueChange}
        />
        {items.length > 0 && (
          <CommandList>
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            {Object.entries(groupedItems).map(([groupName, groupItems]) => (
              <CommandGroup key={groupName} heading={groupName}>
                {groupItems.map((item) => (
                  <CommandItem
                    key={item.id}
                    onSelect={() => handleSelect(item.value)}
                  >
                    {item.label}
                  </CommandItem>
                ))}
              </CommandGroup>
            ))}
          </CommandList>
        )}
      </Command>
    </div>
  )
})

Search.displayName = "Search"

export { Search, type SearchItem } 