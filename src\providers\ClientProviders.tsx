'use client';

import { SessionProvider } from 'next-auth/react';
import { SidebarProvider } from '@/components/ui/sidebar';
import { ThemeProvider } from '@/components/theme-provider';
import { Toaster } from '@/components/ui/toaster';
import { Toaster as SonnerToaster } from '@/components/ui/sonner';
import QueryProvider from './QueryProvider';
import { AsyncErrorBoundary } from '@/components/ErrorBoundary';

interface ClientProvidersProps {
  children: React.ReactNode;
}

export default function ClientProviders({ children }: ClientProvidersProps) {
  return (
    <ThemeProvider 
      attribute="class" 
      defaultTheme="system" 
      enableSystem 
      disableTransitionOnChange
    >
      <AsyncErrorBoundary>
        <SessionProvider>
          <QueryProvider>
            <SidebarProvider>
              {children}
              <Toaster />
              <SonnerToaster position="top-left" dir="rtl" />
            </SidebarProvider>
          </QueryProvider>
        </SessionProvider>
      </AsyncErrorBoundary>
    </ThemeProvider>
  );
}
