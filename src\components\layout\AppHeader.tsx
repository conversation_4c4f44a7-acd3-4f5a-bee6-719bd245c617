"use client";

import * as React from "react";
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Bell, 
  Search, 
  Users, 
  FileText, 
  Plus,
  Menu,
  LogOut,
  User,
  Settings,
  HelpCircle
} from 'lucide-react';
import { APP_NAME } from '@/lib/constants';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { ThemeToggle } from "@/components/ui/theme-toggle"
import { useKeyboardShortcuts } from "@/hooks/use-keyboard-shortcuts"
import { useChildren, useAssessments } from "@/hooks/use-storage"
import { calculateAge, formatDate } from "@/lib/utils"
import { differenceInDays, parseISO, addMonths, isPast, isValid } from "date-fns"
import { NotificationBell } from "@/components/notifications/NotificationBell"
import { useSession, signOut } from "next-auth/react"
import { toast } from "sonner"

export default function AppHeader() {
  const [searchOpen, setSearchOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState("")
  const { data: session } = useSession()
  const router = useRouter()

  const { children, isLoading: childrenLoading } = useChildren()
  const { assessments } = useAssessments()

  // Set up keyboard shortcuts
  useKeyboardShortcuts({
    'ctrl+k': () => setSearchOpen(true),
    'cmd+k': () => setSearchOpen(true),
  })



  // Search items
  const searchItems = React.useMemo(() => {
    if (childrenLoading) return []
    
    const items: Array<{
      id: string
      label: string
      value: string
      group: string
      href: string
      icon: any
    }> = []
    
    // Add children to search
    children.forEach(child => {
      items.push({
        id: `child-${child.id}`,
        label: child.name,
        value: child.name.toLowerCase(),
        group: "الأطفال",
        href: `/children/${child.id}`,
        icon: Users
      })
    })

    // Add assessments to search
    assessments.forEach(assessment => {
      const child = children.find(c => c.id === assessment.childId)
      if (child) {
        items.push({
          id: `assessment-${assessment.id}`,
          label: `تقييم ${child.name} - ${formatDate(assessment.assessmentDate)}`,
          value: `تقييم ${child.name}`.toLowerCase(),
          group: "التقييمات",
          href: `/children/${assessment.childId}/assessment/${assessment.id}`,
          icon: FileText
        })
      }
    })

    return items
  }, [children, assessments, childrenLoading])

  const filteredItems = searchItems.filter(item =>
    item.value.includes(searchValue.toLowerCase())
  )



  const handleLogout = async () => {
    try {
      await signOut({ redirect: false })
      toast.success("تم تسجيل الخروج بنجاح")
      router.push("/login")
      router.refresh()
    } catch (error) {
      console.error('Logout error:', error)
      toast.error("حدث خطأ أثناء تسجيل الخروج")
    }
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex h-16 items-center px-2 md:px-4 lg:px-6 gap-2 md:gap-4 overflow-hidden">
        {/* Mobile sidebar trigger */}
        <div className="md:hidden flex-shrink-0">
          <SidebarTrigger className="h-8 w-8" />
        </div>
        
        {/* Logo and brand */}
        <div className="flex items-center gap-2 md:gap-4 lg:gap-6 flex-shrink-0">
          {/* Removed logo/brand section to avoid duplication with sidebar */}
        </div>

        {/* Search */}
        <div className="header-search flex-1 max-w-sm mx-2 md:max-w-md md:mx-4 min-w-0">
          <Popover open={searchOpen} onOpenChange={setSearchOpen}>
            <PopoverTrigger asChild>
              <Button 
                variant="outline" 
                className="relative w-full justify-start text-sm text-muted-foreground h-9 px-2 md:px-3 min-w-0"
              >
                <Search className="h-4 w-4 shrink-0 ml-1 md:ml-2" />
                {/* Show full text on md+ screens, just icon on mobile */}
                <span className="hidden md:flex flex-1 text-right truncate">
                  البحث في الأطفال والتقييمات...
                </span>
                <span className="md:hidden flex-1 text-right text-xs truncate">
                  بحث...
                </span>
                <kbd className="pointer-events-none hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100 lg:flex">
                  <span className="text-xs">Ctrl</span>K
                </kbd>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[90vw] md:w-96 p-0" align="end">
              <Command className="rounded-lg border-0 shadow-md">
                <CommandInput 
                  placeholder="البحث في الأطفال والتقييمات..."
                  value={searchValue}
                  onValueChange={setSearchValue}
                  className="h-9"
                />
                <CommandList className="max-h-64">
                  <CommandEmpty className="py-6 text-center text-sm text-muted-foreground">
                    لا توجد نتائج للبحث
                  </CommandEmpty>
                  {Object.entries(
                    filteredItems.reduce((groups, item) => {
                      if (!groups[item.group]) groups[item.group] = []
                      groups[item.group].push(item)
                      return groups
                    }, {} as Record<string, typeof filteredItems>)
                  ).map(([group, items]) => (
                    <CommandGroup key={group} heading={group}>
                      {items.slice(0, 5).map((item) => (
                        <CommandItem
                          key={item.id}
                          value={item.value}
                          onSelect={() => {
                            setSearchOpen(false)
                            setSearchValue("")
                          }}
                          className="flex items-center gap-3 px-3 py-2"
                          asChild
                        >
                          <Link href={item.href}>
                            <item.icon className="h-4 w-4 shrink-0" />
                            <span className="flex-1 text-right truncate">
                              {item.label}
                            </span>
                          </Link>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  ))}
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        </div>

        <div className="header-actions flex items-center gap-1 md:gap-2 flex-shrink-0">
          {/* Notifications */}
          <NotificationBell />

          {/* Theme Toggle */}
          <div className="hidden sm:block">
            <ThemeToggle />
          </div>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 rounded-full p-0">
                <Avatar className="h-7 w-7 md:h-8 md:w-8">
                  <AvatarImage src="/placeholder-avatar.jpg" alt="المستخدم" />
                  <AvatarFallback className="text-[10px] md:text-xs font-medium bg-primary text-primary-foreground">
                    {session?.user?.name?.slice(0, 2)?.toUpperCase() || 'أح'}
                  </AvatarFallback>
                </Avatar>
                <span className="sr-only">قائمة المستخدم</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-48 md:w-56" align="end">
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1 text-right">
                  <p className="text-sm font-medium leading-none">{session?.user?.name || 'المستخدم'}</p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {session?.user?.email || '<EMAIL>'}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/profile" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  الملف الشخصي
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/settings" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  الإعدادات
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/help" className="flex items-center gap-2">
                  <HelpCircle className="h-4 w-4" />
                  المساعدة
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                className="text-destructive flex items-center gap-2 cursor-pointer"
                onClick={handleLogout}
              >
                <LogOut className="h-4 w-4" />
                تسجيل الخروج
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
