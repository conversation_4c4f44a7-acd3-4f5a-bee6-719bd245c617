"use client";

import React, { useState, useMemo, useEffect } from 'react';
import { PORTAGE_CHECKLIST_DATA, SKILL_STATUS_OPTIONS, PROGRESS_STATUS_OPTIONS } from '@/lib/constants';
import { useChildren, useAssessments } from '@/hooks/use-storage';
import type { Child, Assessment, PortageSkillItem, PortageDimension, PortageSubCategory, SkillStatus, ProgressStatus } from '@/lib/types';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowRight, Edit } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { formatDate } from '@/lib/utils';
import AgeDisplay from '@/components/assessment/AgeDisplay';
import AnalyzableSkillItem, { type AnalyzableItemType as ImportedAnalyzableItemType } from '@/components/assessment/AnalyzableSkillItem';
import GlobalPlanNotes from '@/components/assessment/GlobalPlanNotes';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { use } from 'react';

// Enhanced AnalyzableItemType for internal use in this page, including IDs for filtering
type PageAnalyzableItemType = ImportedAnalyzableItemType & {
  dimensionId: string;
  subCategoryId: string;
};

const ALL_DIMENSIONS_VALUE = "all-dimensions";
const ALL_SUBCATEGORIES_VALUE = "all-subcategories";
const ALL_SKILL_STATUS_VALUE = "all-skill-status"; // Renamed from ALL_STATUS_VALUE for clarity
const ALL_PROGRESS_STATUS_VALUE = "all-progress-status";




// This function needs to be available client-side or its result passed down.
const getSkillDetails = (skillId: string): (PortageSkillItem & { dimensionId: string, dimensionName: string; subCategoryId: string, subCategoryName: string; }) | null => {
  for (const dimension of PORTAGE_CHECKLIST_DATA) {
    for (const subCategory of dimension.subCategories) {
      const skill = subCategory.skills.find(s => s.id === skillId);
      if (skill) return { ...skill, dimensionId: dimension.id, dimensionName: dimension.name, subCategoryId: subCategory.id, subCategoryName: subCategory.name };
    }
  }
  return null;
};

interface AnalysisResult {
  baselineSkill: PageAnalyzableItemType | null;
  ceilingSkill: PageAnalyzableItemType | null;
  teachingRangeSkills: PageAnalyzableItemType[];
}

interface SubCategoryAnalysisData extends AnalysisResult {
  subCategoryName: string;
  skillsCount: number;
}

interface DimensionAnalysisData {
  dimensionName: string;
  subCategories: SubCategoryAnalysisData[];
}

function calculateAnalysisForSubCategory(skillsInSubCategory: PageAnalyzableItemType[]): AnalysisResult {
  const sortedSkills = [...skillsInSubCategory].sort((a, b) => {
    const numA = parseInt(a.itemNumber, 10);
    const numB = parseInt(b.itemNumber, 10);
    return numA - numB;
  });

  let baselineSkill: PageAnalyzableItemType | null = null;
  let ceilingSkill: PageAnalyzableItemType | null = null;
  const teachingRangeSkills: PageAnalyzableItemType[] = [];

  // Baseline: highest numbered skill in the first sequence of 3 consecutive "yes"
  for (let i = 0; i <= sortedSkills.length - 3; i++) {
    if (
      sortedSkills[i].status === 'yes' &&
      sortedSkills[i + 1].status === 'yes' &&
      sortedSkills[i + 2].status === 'yes'
    ) {
      baselineSkill = sortedSkills[i + 2]; // The highest of the three
      break;
    }
  }
  // If no 3 consecutive "yes", baseline is the lowest numbered "yes" skill
  if (!baselineSkill) {
    baselineSkill = sortedSkills.find(skill => skill.status === 'yes') || null;
  }


  // Ceiling: lowest numbered skill in the first sequence of 3 consecutive "no"
  for (let i = 0; i <= sortedSkills.length - 3; i++) {
    if (
      sortedSkills[i].status === 'no' &&
      sortedSkills[i + 1].status === 'no' &&
      sortedSkills[i + 2].status === 'no'
    ) {
      ceilingSkill = sortedSkills[i]; // The lowest of the three
      break;
    }
  }
  // If no 3 consecutive "no", ceiling is the highest numbered "no" skill
  if (!ceilingSkill) {
    let highestNoSkill: PageAnalyzableItemType | null = null;
    for (let i = sortedSkills.length - 1; i >= 0; i--) {
      if (sortedSkills[i].status === 'no') {
        highestNoSkill = sortedSkills[i];
        break;
      }
    }
    if (highestNoSkill) {
      ceilingSkill = highestNoSkill;
    }
  }

  let teachingStartIndex = 0;
  if (baselineSkill) {
    const baselineIdx = sortedSkills.findIndex(s => s.skillId === baselineSkill!.skillId);
    if (baselineIdx !== -1) {
      teachingStartIndex = baselineIdx + 1; // Start teaching skills *after* the baseline
    }
  }

  for (let i = teachingStartIndex; i < sortedSkills.length; i++) {
    const skill = sortedSkills[i];
    const skillItemNumber = parseInt(skill.itemNumber, 10);

    // Stop if we've passed the ceiling skill (current skill is numerically greater than ceiling)
    if (ceilingSkill && skillItemNumber > parseInt(ceilingSkill.itemNumber, 10)) {
      break;
    }
    // Include skills marked 'no' or 'unclear' within the teaching range
    if (skill.status === 'no' || skill.status === 'unclear') {
      teachingRangeSkills.push(skill);
    }
  }

  return { baselineSkill, ceilingSkill, teachingRangeSkills };
}

// Props for the client component part
interface ViewAssessmentClientProps {
  child: Child;
  assessment: Assessment;
  portageChecklist: PortageDimension[]; // Pass the whole checklist
}

function ViewAssessmentClientContent({ child, assessment, portageChecklist }: ViewAssessmentClientProps) {
  const [selectedDimensionId, setSelectedDimensionId] = useState<string>(ALL_DIMENSIONS_VALUE);
  const [selectedSubCategoryId, setSelectedSubCategoryId] = useState<string>(ALL_SUBCATEGORIES_VALUE);
  const [selectedSkillStatus, setSelectedSkillStatus] = useState<string>(ALL_SKILL_STATUS_VALUE);
  const [selectedProgressStatus, setSelectedProgressStatus] = useState<string>(ALL_PROGRESS_STATUS_VALUE);


  const dimensionOptions = useMemo(() => {
    if (!portageChecklist || !Array.isArray(portageChecklist)) {
      return [{ value: ALL_DIMENSIONS_VALUE, label: "الكل - البعد" }];
    }
    return [{ value: ALL_DIMENSIONS_VALUE, label: "الكل - البعد" }, ...portageChecklist.map(d => ({ value: d.id, label: d.name }))];
  }, [portageChecklist]);

  const subCategoryOptions = useMemo(() => {
    const options = [{ value: ALL_SUBCATEGORIES_VALUE, label: "الكل - المجال" }];
    if (selectedDimensionId !== ALL_DIMENSIONS_VALUE) {
      const dimension = portageChecklist.find(d => d.id === selectedDimensionId);
      if (dimension) {
        dimension.subCategories.forEach(sc => {
          options.push({ value: sc.id, label: sc.name });
        });
      }
    }
    return options;
  }, [selectedDimensionId, portageChecklist]);

  const skillStatusOptions = useMemo(() => {
    return [{ value: ALL_SKILL_STATUS_VALUE, label: "الكل - حالة المهارة" }, ...SKILL_STATUS_OPTIONS.map(s => ({ value: s.value, label: s.label }))]
  }, []);

  const progressStatusOptions = useMemo(() => {
    return [{ value: ALL_PROGRESS_STATUS_VALUE, label: "الكل - حالة التقدم" }, ...PROGRESS_STATUS_OPTIONS.map(s => ({ value: s.value, label: s.label }))]
  }, []);

  const allAnalyzableSkills: PageAnalyzableItemType[] = useMemo(() => {
    if (!assessment?.assessedSkills || !Array.isArray(assessment.assessedSkills)) {
      return [];
    }

    return assessment.assessedSkills.map(assessedSkill => {
      const details = getSkillDetails(assessedSkill.skillId);
      if (!details) {
        return null;
      }
      return {
        ...assessedSkill,
        ...details,
        skillId: assessedSkill.skillId,
      };
    }).filter(Boolean) as PageAnalyzableItemType[];
  }, [assessment?.assessedSkills]);

  const filteredSkills = useMemo(() => {
    return allAnalyzableSkills.filter(skill => {
      const dimensionMatch = selectedDimensionId === ALL_DIMENSIONS_VALUE || skill.dimensionId === selectedDimensionId;
      const subCategoryMatch = selectedSubCategoryId === ALL_SUBCATEGORIES_VALUE || skill.subCategoryId === selectedSubCategoryId;
      const skillStatusMatch = selectedSkillStatus === ALL_SKILL_STATUS_VALUE || skill.status === selectedSkillStatus;

      let progressStatusMatch = selectedProgressStatus === ALL_PROGRESS_STATUS_VALUE;
      if (!progressStatusMatch) {
        if (selectedProgressStatus === 'pending') {
          progressStatusMatch = skill.progressStatus === 'pending' || skill.progressStatus === undefined;
        } else {
          progressStatusMatch = skill.progressStatus === selectedProgressStatus;
        }
      }

      return dimensionMatch && subCategoryMatch && skillStatusMatch && progressStatusMatch;
    });
  }, [allAnalyzableSkills, selectedDimensionId, selectedSubCategoryId, selectedSkillStatus, selectedProgressStatus]);

  const finalGroupedSkillsForDisplay: Record<string, PageAnalyzableItemType[]> = useMemo(() => {
    const grouped: Record<string, PageAnalyzableItemType[]> = {};
    filteredSkills.forEach(skill => {
      if (!grouped[skill.dimensionName]) {
        grouped[skill.dimensionName] = [];
      }
      grouped[skill.dimensionName].push(skill);
    });
    for (const dimName in grouped) {
      grouped[dimName].sort((a, b) => parseInt(a.itemNumber, 10) - parseInt(b.itemNumber, 10));
    }
    return grouped;
  }, [filteredSkills]);


  const allAnalysisData: DimensionAnalysisData[] = useMemo(() => {
    return portageChecklist.map(dimension => {
      const subCategoriesData: SubCategoryAnalysisData[] = dimension.subCategories.map(subCategory => {
        const skillsForSubCategory: PageAnalyzableItemType[] = [];
        assessment.assessedSkills.forEach(assessedSkill => {
          const details = getSkillDetails(assessedSkill.skillId);
          if (details && details.dimensionId === dimension.id && details.subCategoryId === subCategory.id) {
            skillsForSubCategory.push({
              ...assessedSkill,
              ...details,
              skillId: assessedSkill.skillId,
            });
          }
        });
        skillsForSubCategory.sort((a, b) => parseInt(a.itemNumber, 10) - parseInt(b.itemNumber, 10));
        const analysis = calculateAnalysisForSubCategory(skillsForSubCategory);
        return {
          subCategoryName: subCategory.name,
          skillsCount: skillsForSubCategory.length,
          ...analysis,
        };
      });

      return {
        dimensionName: dimension.name,
        subCategories: subCategoriesData.filter(sc => sc.skillsCount > 0),
      };
    }).filter(dim => dim.subCategories.length > 0);
  }, [assessment.assessedSkills, portageChecklist]);


  return (
    <div className="container mx-auto py-8">
      <Link href={`/children/${child.id}/assessment`} className="inline-flex items-center gap-2 text-sm text-primary hover:underline mb-4">
        العودة إلى سجل تقييمات {child.name}
        <ArrowRight className="h-4 w-4" />
      </Link>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-primary">تفاصيل التقييم لـ {child.name}</h1>
          <p className="text-muted-foreground">تاريخ التقييم: {formatDate(assessment.assessmentDate)}</p>
          <AgeDisplay birthDate={child.birthDate} assessmentDate={assessment.assessmentDate} label="عمر الطفل عند التقييم:" className="text-sm" />
        </div>
        <Link href={`/children/${child.id}/assessment/${assessment.id}/edit`}>
          <Button variant="outline">
            <Edit className="ml-2 h-4 w-4" /> تعديل التقييم
          </Button>
        </Link>
      </div>

      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle>المهارات المقيمة</CardTitle>
          <CardDescription>مراجعة أداء الطفل في كل بند مهارة. استخدم الفلاتر أدناه لتسهيل التصفح.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-6 p-4 border rounded-lg shadow-sm bg-muted/30">
            <h3 className="text-lg font-semibold mb-3 text-primary">فلاتر العرض</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="dimensionFilterDisplay" className="mb-1 block text-sm font-medium">البعد:</Label>
                <Select value={selectedDimensionId} onValueChange={setSelectedDimensionId}>
                  <SelectTrigger id="dimensionFilterDisplay"><SelectValue placeholder="اختر البعد" /></SelectTrigger>
                  <SelectContent>
                    {dimensionOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="subCategoryFilterDisplay" className="mb-1 block text-sm font-medium">المجال:</Label>
                <Select value={selectedSubCategoryId} onValueChange={setSelectedSubCategoryId} disabled={selectedDimensionId === ALL_DIMENSIONS_VALUE && subCategoryOptions.length <= 1}>
                  <SelectTrigger id="subCategoryFilterDisplay"><SelectValue placeholder="اختر المجال" /></SelectTrigger>
                  <SelectContent>
                    {subCategoryOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="skillStatusFilterDisplay" className="mb-1 block text-sm font-medium">حالة المهارة:</Label>
                <Select value={selectedSkillStatus} onValueChange={setSelectedSkillStatus}>
                  <SelectTrigger id="skillStatusFilterDisplay"><SelectValue placeholder="اختر حالة المهارة" /></SelectTrigger>
                  <SelectContent>
                    {skillStatusOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="progressStatusFilterDisplay" className="mb-1 block text-sm font-medium">حالة التقدم:</Label>
                <Select value={selectedProgressStatus} onValueChange={setSelectedProgressStatus}>
                  <SelectTrigger id="progressStatusFilterDisplay"><SelectValue placeholder="اختر حالة التقدم" /></SelectTrigger>
                  <SelectContent>
                    {progressStatusOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Global Plan Notes */}
          <GlobalPlanNotes childId={child.id} childName={child.name} />

          {Object.keys(finalGroupedSkillsForDisplay).length > 0 ? (
            <Accordion type="multiple" className="w-full" defaultValue={Object.keys(finalGroupedSkillsForDisplay)}>
              {Object.entries(finalGroupedSkillsForDisplay).map(([dimensionName, skills]) => (
                <AccordionItem value={dimensionName} key={dimensionName}>
                  <AccordionTrigger className="text-xl font-semibold text-accent">{dimensionName}</AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-4 pr-4">
                      {skills.map((item) => (
                        <AnalyzableSkillItem
                          key={item.skillId}
                          item={item}
                          childName={child.name}
                          childId={child.id}
                          assessmentId={assessment.id}
                        />
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          ) : (
            <p className="text-muted-foreground text-center py-6">لا توجد مهارات تطابق معايير الفلترة المحددة.</p>
          )}
        </CardContent>
      </Card>

      <Card className="mt-6 shadow-lg">
        <CardHeader>
          <CardTitle>التحليل: الخط القاعدي، الخط السقفي، ومنطقة التعلم</CardTitle>
          <CardDescription>يستند هذا التحليل إلى قواعد بورتيج القياسية المطبقة على **جميع** المهارات المقيمة في هذا التقييم.</CardDescription>
        </CardHeader>
        <CardContent>
          {allAnalysisData.length > 0 ? (
            <Accordion type="multiple" className="w-full" defaultValue={allAnalysisData.map(d => d.dimensionName)}>
              {allAnalysisData.map(dimensionData => (
                <AccordionItem value={dimensionData.dimensionName} key={dimensionData.dimensionName}>
                  <AccordionTrigger className="text-xl font-semibold text-primary">{dimensionData.dimensionName}</AccordionTrigger>
                  <AccordionContent>
                    {dimensionData.subCategories.map(subCategoryData => (
                      <div key={subCategoryData.subCategoryName} className="mb-4 p-3 border rounded-md bg-muted/50">
                        <h4 className="text-lg font-medium text-accent mb-2">{subCategoryData.subCategoryName}</h4>
                        <p><strong>الخط القاعدي:</strong> {subCategoryData.baselineSkill ? `${subCategoryData.baselineSkill.itemNumber}. ${subCategoryData.baselineSkill.behavior}` : "لم يتم تحديده"}</p>
                        <p><strong>الخط السقفي:</strong> {subCategoryData.ceilingSkill ? `${subCategoryData.ceilingSkill.itemNumber}. ${subCategoryData.ceilingSkill.behavior}` : "لم يتم تحديده"}</p>
                        {subCategoryData.teachingRangeSkills.length > 0 ? (
                          <div>
                            <h5 className="font-semibold mt-2">منطقة التعلم (المهارات المقترحة للتركيز عليها):</h5>
                            <ul className="list-disc pr-5 mt-1 space-y-1 text-sm">
                              {subCategoryData.teachingRangeSkills.map(skill => (
                                <li key={skill.skillId}>{skill.itemNumber}. {skill.behavior} ({skill.ageRange})</li>
                              ))}
                            </ul>
                          </div>
                        ) : (
                          <p className="mt-2 text-sm text-muted-foreground">لا توجد مهارات محددة في منطقة التعلم بناءً على هذا التحليل (قد تكون جميع المهارات حتى الخط السقفي متقنة، أو لم يتم تحديد خط أساس واضح، أو لم يتم تحديد سقف واضح).</p>
                        )}
                      </div>
                    ))}
                    {dimensionData.subCategories.length === 0 && <p className="text-sm text-muted-foreground">لا توجد بيانات تحليل لهذه الفئة الفرعية.</p>}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          ) : (
            <p className="text-muted-foreground">لا يمكن إنشاء التحليل. قد لا تكون هناك بيانات تقييم كافية أو مهارات مقيمة.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// This is the main page component which will fetch data and pass to client component
export default function ViewAssessmentPage({ params }: { params: Promise<{ childId: string, assessmentId: string }> }) {
  const resolvedParams = use(params);
  const { getChild, loading: childrenLoading } = useChildren();
  const { getAssessment, loading: assessmentsLoading } = useAssessments(); // Get all assessments

  const [child, setChild] = useState<Child | null>(null);
  const [assessment, setAssessment] = useState<Assessment | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Wait for hooks to be ready
    if (childrenLoading || assessmentsLoading) {
      return;
    }

    const childData = getChild(resolvedParams.childId);
    const assessmentData = getAssessment(resolvedParams.assessmentId);

    setChild(childData || null);
    setAssessment(assessmentData || null);
    setLoading(false);
  }, [resolvedParams.childId, resolvedParams.assessmentId, getChild, getAssessment, childrenLoading, assessmentsLoading]);

  if (loading || childrenLoading || assessmentsLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري تحميل بيانات التقييم...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!child || !assessment) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h1 className="text-2xl font-semibold">التقييم أو الطفل غير موجود</h1>
        <Link href={`/children/${resolvedParams.childId}/assessment`}>
          <Button variant="link">العودة إلى سجل التقييمات</Button>
        </Link>
      </div>
    );
  }

  return <ViewAssessmentClientContent child={child} assessment={assessment} portageChecklist={PORTAGE_CHECKLIST_DATA} />;
}



