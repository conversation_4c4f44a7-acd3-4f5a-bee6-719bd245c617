@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* New Modern Theme */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --chart-1: 221.2 83.2% 53.3%;
    --chart-2: 166.2 83.2% 53.3%;
    --chart-3: 351.2 83.2% 53.3%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.75rem;

    /* Sidebar colors */
    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 210 40% 98%;
    --sidebar-primary-foreground: 222.2 84% 4.9%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 215 20.2% 65.1%;
  }

  .dark {
    /* New Modern Dark Theme */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 215 20.2% 65.1%;
    --chart-1: 210 40% 98%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* Dark Sidebar colors */
    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 210 40% 98%;
    --sidebar-primary-foreground: 222.2 84% 4.9%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 215 20.2% 65.1%;
  }
}

@layer base {
  html {
    direction: rtl;
    scroll-behavior: smooth;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  * {
    @apply border-border;
    box-sizing: border-box;
  }
  
  *::before,
  *::after {
    box-sizing: border-box;
  }
  
  body {
    @apply bg-background text-foreground;
    font-family: 'Tajawal', 'Noto Sans Arabic', 'IBM Plex Sans Arabic', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1, "kern" 1;
    line-height: 1.6;
    letter-spacing: 0.01em;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
    min-height: 100vh;
  }

  /* Enhanced Arabic typography */
  .arabic-text {
    font-feature-settings: "rlig" 1, "calt" 1, "kern" 1, "liga" 1;
    text-rendering: optimizeLegibility;
    line-height: 1.7;
    letter-spacing: 0.02em;
  }

  /* Fix sidebar positioning and remove gaps */
  [data-sidebar="sidebar"] {
    @apply w-full h-full;
    direction: rtl;
    text-align: right;
  }

  /* Ensure proper RTL layout for sidebar components */
  [data-sidebar="sidebar"] * {
    direction: rtl;
  }

  /* Remove any unwanted margins that could cause gaps */
  .group\/sidebar-wrapper {
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Fix potential gap issues on the right side */
  [data-side="right"] {
    right: 0 !important;
    border-left: 1px solid hsl(var(--sidebar-border));
    border-right: none !important;
  }

  /* Ensure sidebar content is properly aligned */
  [data-sidebar="content"] {
    width: 100%;
    height: 100%;
    overflow: visible;
  }

  /* Mobile responsive fixes */
  @media (max-width: 768px) {
    .sidebar-fixed {
      position: relative !important;
      width: 100% !important;
      margin: 0 !important;
    }
    
    .main-content {
      margin-right: 0 !important;
      width: 100% !important;
    }

    /* Hide sidebar spacer on mobile */
    [data-side="right"] > div:first-child {
      display: block !important;
      width: 0 !important;
    }

    /* Reset sidebar positioning on mobile */
    [data-side="right"] > div:last-child {
      position: relative !important;
      right: auto !important;
      width: 100% !important;
      height: auto !important;
      top: auto !important;
    }

    /* Adjust main content margins on mobile */
    .layout-main {
      margin-right: 0 !important;
      width: 100% !important;
    }

    /* Prevent horizontal scroll in header */
    header {
      overflow-x: hidden;
      min-width: 0;
    }

    header > div {
      min-width: 0;
      max-width: 100vw;
    }

    /* Ensure search and actions don't overflow */
    .header-search {
      min-width: 0;
      flex-shrink: 1;
    }

    .header-actions {
      flex-shrink: 0;
      min-width: 0;
    }
  }

  /* Desktop sidebar positioning */
  @media (min-width: 769px) {
    .sidebar-fixed {
      position: fixed !important;
      right: 0 !important;
      top: 0 !important;
      bottom: 0 !important;
      width: var(--sidebar-width) !important;
      z-index: 50;
    }
    
    .main-content {
      margin-right: var(--sidebar-width) !important;
      margin-left: 0 !important;
    }

    /* Ensure main content has correct layout on desktop */
    .layout-main {
      margin-right: var(--sidebar-width) !important;
      width: calc(100vw - var(--sidebar-width)) !important;
    }
  }

  /* Ensure proper sidebar background coverage */
  [data-sidebar="sidebar"] {
    background: hsl(var(--sidebar-background));
    border-left: 1px solid hsl(var(--sidebar-border));
    border-right: none;
  }

  /* Hide the spacer div that causes the right gap */
  [data-side="right"] > div:first-child {
    display: none !important;
  }

  /* Force right sidebar to be positioned correctly */
  [data-side="right"] > div:last-child {
    position: fixed !important;
    right: 0 !important;
    left: auto !important;
    width: var(--sidebar-width) !important;
    height: 100vh !important;
    top: 0 !important;
    z-index: 50 !important;
  }

  /* Better scrollbar styling for RTL */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.2);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.3);
  }

  /* Enhanced button and interactive element styles */
  button, [role="button"] {
    cursor: pointer;
    transition: all 0.2s ease-in-out;
  }

  button:disabled, [role="button"]:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  /* Focus styles for better accessibility */
  button:focus-visible,
  [role="button"]:focus-visible,
  input:focus-visible,
  textarea:focus-visible,
  select:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* Smooth transitions */
  .transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }
}

@layer components {
  /* Enhanced card styling */
  .card {
    @apply bg-card text-card-foreground shadow-sm border border-border rounded-lg;
    backdrop-filter: blur(8px);
  }

  /* Better button variants */
  .btn-primary {
    @apply bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50;
    transition: all 0.2s ease-in-out;
  }

  /* Improved input styling */
  .input {
    @apply border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
    direction: rtl;
    text-align: right;
  }

  /* Enhanced sidebar menu styling */
  .sidebar-menu-item {
    @apply rounded-md transition-all duration-200 hover:bg-sidebar-accent/80 focus:bg-sidebar-accent focus:outline-none;
  }

  .sidebar-menu-item.active {
    @apply bg-sidebar-accent text-sidebar-accent-foreground font-medium shadow-sm;
  }
}

@layer utilities {
  /* RTL-specific utilities */
  .rtl {
    direction: rtl;
  }

  .ltr {
    direction: ltr;
  }

  /* Text direction utilities */
  .text-right-rtl {
    text-align: right;
    direction: rtl;
  }

  .text-left-ltr {
    text-align: left;
    direction: ltr;
  }

  /* Spacing utilities for RTL */
  .mr-auto-rtl {
    margin-left: auto;
    margin-right: 0;
  }

  .ml-auto-rtl {
    margin-right: auto;
    margin-left: 0;
  }

  /* Hide scrollbar */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Enhanced hover effects */
  .hover-lift {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  }

  .hover-lift:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  /* Glass effect */
  .glass {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}
