"use client";

import React from 'react';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  AlertTriangle, 
  Clock, 
  CheckCircle, 
  FileText, 
  ClipboardList, 
  Info,
  X 
} from 'lucide-react';
import { formatDistanceToNow, parseISO } from 'date-fns';
import { arSA } from 'date-fns/locale';
import { Notification, NotificationType } from '@/lib/types';
import { useNotifications } from '@/hooks/useNotifications';

interface NotificationItemProps {
  notification: Notification;
  onClose?: () => void;
}

// Get notification icon based on type
function getNotificationIcon(type: NotificationType) {
  switch (type) {
    case 'assessment_overdue':
      return <AlertTriangle className="h-4 w-4 text-red-500" />;
    case 'assessment_due_soon':
      return <Clock className="h-4 w-4 text-yellow-500" />;
    case 'service_complete':
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    case 'plan_created':
      return <FileText className="h-4 w-4 text-blue-500" />;
    case 'session_note_added':
      return <ClipboardList className="h-4 w-4 text-purple-500" />;
    case 'system':
      return <Info className="h-4 w-4 text-gray-500" />;
    default:
      return <Info className="h-4 w-4 text-gray-500" />;
  }
}

// Get notification color based on type and urgency
function getNotificationColor(type: NotificationType, urgent?: boolean) {
  if (urgent) return 'destructive';
  
  switch (type) {
    case 'assessment_overdue':
      return 'destructive';
    case 'assessment_due_soon':
      return 'secondary';
    case 'service_complete':
      return 'default';
    case 'plan_created':
      return 'default';
    case 'session_note_added':
      return 'default';
    case 'system':
      return 'outline';
    default:
      return 'secondary';
  }
}

// Generate link based on notification
function getNotificationLink(notification: Notification): string {
  if (notification.childId) {
    return `/children/${notification.childId}`;
  }
  
  // Default to dashboard
  return '/dashboard';
}

export function NotificationItem({ notification, onClose }: NotificationItemProps) {
  const { markAsRead, deleteNotification } = useNotifications();
  
  const handleClick = async () => {
    if (!notification.isRead) {
      try {
        await markAsRead(notification.id);
      } catch (error) {
        console.error('Error marking notification as read:', error);
      }
    }
    onClose?.();
  };

  const handleDelete = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    try {
      await deleteNotification(notification.id);
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };

  const timeAgo = formatDistanceToNow(parseISO(notification.createdAt), {
    addSuffix: true,
    locale: arSA,
  });

  const urgent = notification.metadata?.urgent || false;
  const link = getNotificationLink(notification);

  return (
    <div className={`relative group ${!notification.isRead ? 'bg-muted/30' : ''}`}>
      <Link
        href={link}
        className="block p-3 hover:bg-muted/50 transition-colors"
        onClick={handleClick}
      >
        <div className="flex items-start gap-3">
          {/* Notification Icon */}
          <div className="shrink-0 mt-0.5">
            {getNotificationIcon(notification.type)}
          </div>
          
          {/* Notification Content */}
          <div className="flex-1 min-w-0 space-y-1">
            <div className="flex items-center gap-2">
              <p className="text-sm font-medium text-right flex-1 leading-tight">
                {notification.title}
              </p>
              <Badge
                variant={getNotificationColor(notification.type, urgent)}
                className="text-xs shrink-0"
              >
                {urgent ? 'عاجل' : getTypeLabel(notification.type)}
              </Badge>
            </div>
            
            <p className="text-xs text-muted-foreground text-right leading-relaxed">
              {notification.message}
            </p>
            
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">
                {timeAgo}
              </span>
              {!notification.isRead && (
                <div className="h-2 w-2 bg-blue-500 rounded-full shrink-0" />
              )}
            </div>
          </div>
        </div>
      </Link>
      
      {/* Delete Button */}
      <Button
        variant="ghost"
        size="sm"
        className="absolute top-2 left-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
        onClick={handleDelete}
      >
        <X className="h-3 w-3" />
      </Button>
    </div>
  );
}

// Get type label in Arabic
function getTypeLabel(type: NotificationType): string {
  switch (type) {
    case 'assessment_overdue':
      return 'تقييم متأخر';
    case 'assessment_due_soon':
      return 'تقييم قريب';
    case 'service_complete':
      return 'خدمة مكتملة';
    case 'plan_created':
      return 'خطة جديدة';
    case 'session_note_added':
      return 'محضر جلسة';
    case 'system':
      return 'نظام';
    default:
      return 'إشعار';
  }
}
