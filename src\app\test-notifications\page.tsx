"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Bell, Mail, TestTube, CheckCircle, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useNotifications } from '@/hooks/useNotifications';

export default function TestNotificationsPage() {
  const { toast } = useToast();
  const { createNotification, fetchNotifications } = useNotifications();
  const [isLoading, setIsLoading] = useState(false);
  const [testEmailLoading, setTestEmailLoading] = useState(false);
  const [generateLoading, setGenerateLoading] = useState(false);
  
  const [notificationForm, setNotificationForm] = useState({
    type: 'system',
    title: '',
    message: '',
    childId: '',
  });

  const [testEmail, setTestEmail] = useState('');

  const handleCreateNotification = async () => {
    if (!notificationForm.title || !notificationForm.message) {
      toast({
        title: "خطأ",
        description: "يرجى ملء جميع الحقول المطلوبة.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      await createNotification({
        type: notificationForm.type as any,
        title: notificationForm.title,
        message: notificationForm.message,
        childId: notificationForm.childId || undefined,
        metadata: {
          test: true,
          createdAt: new Date().toISOString(),
        },
      });

      toast({
        title: "تم الإنشاء",
        description: "تم إنشاء الإشعار بنجاح.",
      });

      // Reset form
      setNotificationForm({
        type: 'system',
        title: '',
        message: '',
        childId: '',
      });

      // Refresh notifications
      await fetchNotifications();
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في إنشاء الإشعار.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendTestEmail = async () => {
    if (!testEmail) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال عنوان البريد الإلكتروني.",
        variant: "destructive",
      });
      return;
    }

    setTestEmailLoading(true);
    try {
      const response = await fetch('/api/email-settings/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ testEmail }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send test email');
      }

      toast({
        title: "تم الإرسال",
        description: "تم إرسال البريد الاختباري بنجاح.",
      });

      setTestEmail('');
    } catch (error) {
      toast({
        title: "خطأ",
        description: error instanceof Error ? error.message : "فشل في إرسال البريد الاختباري.",
        variant: "destructive",
      });
    } finally {
      setTestEmailLoading(false);
    }
  };

  const handleGenerateAssessmentNotifications = async () => {
    setGenerateLoading(true);
    try {
      const response = await fetch('/api/notifications/generate', {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to generate notifications');
      }

      const data = await response.json();

      toast({
        title: "تم التوليد",
        description: `تم إنشاء ${data.created} إشعار تقييم.`,
      });

      // Refresh notifications
      await fetchNotifications();
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في توليد إشعارات التقييم.",
        variant: "destructive",
      });
    } finally {
      setGenerateLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex items-center gap-3 mb-6">
        <TestTube className="h-10 w-10 text-primary" />
        <div>
          <h1 className="text-3xl font-bold text-primary">اختبار نظام الإشعارات</h1>
          <p className="text-muted-foreground">اختبار وظائف الإشعارات والبريد الإلكتروني</p>
        </div>
      </div>

      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          هذه الصفحة مخصصة لاختبار نظام الإشعارات. يمكنك إنشاء إشعارات تجريبية واختبار إرسال البريد الإلكتروني.
        </AlertDescription>
      </Alert>

      {/* Create Test Notification */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            <CardTitle>إنشاء إشعار تجريبي</CardTitle>
          </div>
          <CardDescription>
            إنشاء إشعار جديد لاختبار النظام
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="notification-type">نوع الإشعار</Label>
            <Select
              value={notificationForm.type}
              onValueChange={(value) => setNotificationForm(prev => ({ ...prev, type: value }))}
            >
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="system">نظام</SelectItem>
                <SelectItem value="assessment_overdue">تقييم متأخر</SelectItem>
                <SelectItem value="assessment_due_soon">تقييم قريب</SelectItem>
                <SelectItem value="service_complete">خدمة مكتملة</SelectItem>
                <SelectItem value="plan_created">خطة جديدة</SelectItem>
                <SelectItem value="session_note_added">محضر جلسة</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="notification-title">عنوان الإشعار</Label>
            <Input
              id="notification-title"
              value={notificationForm.title}
              onChange={(e) => setNotificationForm(prev => ({ ...prev, title: e.target.value }))}
              placeholder="عنوان الإشعار"
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="notification-message">رسالة الإشعار</Label>
            <Textarea
              id="notification-message"
              value={notificationForm.message}
              onChange={(e) => setNotificationForm(prev => ({ ...prev, message: e.target.value }))}
              placeholder="محتوى الإشعار"
              className="mt-1"
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="child-id">معرف الطفل (اختياري)</Label>
            <Input
              id="child-id"
              value={notificationForm.childId}
              onChange={(e) => setNotificationForm(prev => ({ ...prev, childId: e.target.value }))}
              placeholder="معرف الطفل"
              className="mt-1"
            />
          </div>

          <Button onClick={handleCreateNotification} disabled={isLoading}>
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin ml-1" />
            ) : (
              <Bell className="h-4 w-4 ml-1" />
            )}
            إنشاء إشعار
          </Button>
        </CardContent>
      </Card>

      {/* Test Email */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            <CardTitle>اختبار البريد الإلكتروني</CardTitle>
          </div>
          <CardDescription>
            إرسال بريد إلكتروني تجريبي
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="test-email">البريد الإلكتروني</Label>
            <Input
              id="test-email"
              type="email"
              value={testEmail}
              onChange={(e) => setTestEmail(e.target.value)}
              placeholder="<EMAIL>"
              className="mt-1"
            />
          </div>

          <Button onClick={handleSendTestEmail} disabled={testEmailLoading}>
            {testEmailLoading ? (
              <Loader2 className="h-4 w-4 animate-spin ml-1" />
            ) : (
              <Mail className="h-4 w-4 ml-1" />
            )}
            إرسال بريد تجريبي
          </Button>
        </CardContent>
      </Card>

      {/* Generate Assessment Notifications */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            <CardTitle>توليد إشعارات التقييم</CardTitle>
          </div>
          <CardDescription>
            توليد إشعارات تلقائية للتقييمات المتأخرة والمطلوبة
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={handleGenerateAssessmentNotifications} disabled={generateLoading}>
            {generateLoading ? (
              <Loader2 className="h-4 w-4 animate-spin ml-1" />
            ) : (
              <CheckCircle className="h-4 w-4 ml-1" />
            )}
            توليد إشعارات التقييم
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
