import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

export async function GET() {
  const planNotes = await prisma.planNote.findMany();
  return NextResponse.json(planNotes);
}

export async function POST(request: Request) {
  const data = await request.json();
  const planNote = await prisma.planNote.create({ data });
  return NextResponse.json(planNote, { status: 201 });
}
