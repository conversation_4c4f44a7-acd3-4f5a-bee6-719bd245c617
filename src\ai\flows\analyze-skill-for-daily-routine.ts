'use server';
/**
 * @fileOverview Provides AI-driven analysis for integrating a child's skill into daily routines.
 *
 * - analyzeSkillForDailyRoutine - Function to generate routine integration suggestions for a skill.
 * - SkillAnalysisInput - Input type for the analysis.
 * - SkillAnalysisOutput - Output type for the analysis.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const SkillAnalysisInputSchema = z.object({
  skillBehavior: z.string().describe('وصف سلوك المهارة أو الهدف المطلوب تحليله.'),
  ageRange: z.string().describe('الفئة العمرية للطفل المستهدف بالمهارة.'),
  childName: z.string().describe('اسم الطفل.'),
});
export type SkillAnalysisInput = z.infer<typeof SkillAnalysisInputSchema>;

const SkillAnalysisOutputSchema = z.object({
  mealtime: z.string().describe("اقتراحات عملية ومفصلة لدمج المهارة في وقت الطعام، مقدمة بلسان الطفل وباللهجة الأردنية."),
  bathroom: z.string().describe("اقتراحات عملية ومفصلة لدمج المهارة في وقت استخدام الحمام، مقدمة بلسان الطفل وباللهجة الأردنية."),
  playtime: z.string().describe("اقتراحات عملية ومفصلة لدمج المهارة في وقت اللعب، مقدمة بلسان الطفل وباللهجة الأردنية."),
  outings: z.string().describe("اقتراحات عملية ومفصلة لدمج المهارة عند الخروج من المنزل، مقدمة بلسان الطفل وباللهجة الأردنية."),
  bedtime: z.string().describe("اقتراحات عملية ومفصلة لدمج المهارة في وقت النوم، مقدمة بلسان الطفل وباللهجة الأردنية."),
  toolsIntegration: z.string().describe("شرح لكيفية استخدام الأدوات المذكورة أو المقترحة في الأنشطة الروتينية، مقدم بلسان الطفل وباللهجة الأردنية."),
  generalTips: z.string().describe("نصائح عامة للأهل أو مقدمي الرعاية لضمان قابلية تطبيق الاقتراحات بشكل واقعي وممتع للطفل، مقدمة بلسان الطفل وباللهجة الأردنية."),
});
export type SkillAnalysisOutput = z.infer<typeof SkillAnalysisOutputSchema>;

export async function analyzeSkillForDailyRoutine(
  input: SkillAnalysisInput
): Promise<SkillAnalysisOutput> {
  // Check if AI is disabled
  if (!ai) {
    return {
      mealtime: "ميزة التحليل التلقائي غير متاحة حاليًا. يرجى إعداد مفتاح Google AI API.",
      bathroom: "ميزة التحليل التلقائي غير متاحة حاليًا. يرجى إعداد مفتاح Google AI API.",
      playtime: "ميزة التحليل التلقائي غير متاحة حاليًا. يرجى إعداد مفتاح Google AI API.",
      outings: "ميزة التحليل التلقائي غير متاحة حاليًا. يرجى إعداد مفتاح Google AI API.",
      bedtime: "ميزة التحليل التلقائي غير متاحة حاليًا. يرجى إعداد مفتاح Google AI API.",
      toolsIntegration: "ميزة التحليل التلقائي غير متاحة حاليًا. يرجى إعداد مفتاح Google AI API.",
      generalTips: "ميزة التحليل التلقائي غير متاحة حاليًا. يرجى الاستعانة بأخصائي تطوير الطفولة المبكرة."
    };
  }
  
  return analyzeSkillFlow(input);
}

// Only define AI flows if AI is available
const prompt = ai?.definePrompt({
  name: 'analyzeSkillForDailyRoutinePrompt',
  input: {schema: SkillAnalysisInputSchema},
  output: {schema: SkillAnalysisOutputSchema},
  prompt: `يا جماعة، أنا {{{childName}}} (عمري {{{ageRange}}}). بدي تساعدوني أتعلم شغلة جديدة ومهمة إلي، وهي: "{{{skillBehavior}}}".

احكوا معي بالعامية الأردنية، وخلوني أحكيلكم كيف بنقدر نخلي تعلم هالشغلة جزء من يومنا العادي، وبطريقة حلوة ومسلية.

أعطوني أفكار واضحة لكل وقت من هالأوقات، كأني أنا اللي بقترحها عليكم:

1.  **وقت الأكل (mealtime):** (يا ماما ويا بابا، وقت الأكل، كيف ممكن نخلي تعلم "{{{skillBehavior}}}" إشي زاكي وممتع؟ مثلاً، أنا جاي عبالي...)
2.  **وقت الحمام (bathroom):** (لما أكون بالحمام، كيف بتقدروا تساعدوني بـ "{{{skillBehavior}}}"؟ بلكي بنقدر نعمل...)
3.  **وقت اللعب (playtime):** (يا سلااام على وقت اللعب! عشان أتعلم "{{{skillBehavior}}}", شو الألعاب اللي بنقدر نلعبها سوا؟ أنا بخطر عبالي...)
4.  **لما نطلع من البيت (outings):** (لما نطلع مشوار، كيف ممكن نستغل الفرص عشان أتدرب على "{{{skillBehavior}}}"؟ مثلاً، بالسوق أو لما نزور قرايبنا...)
5.  **وقت النوم (bedtime):** (قبل ما أنام، في طريقة لطيفة نتذكر فيها "{{{skillBehavior}}}"؟ يمكن عن طريق قصة أو لعبة هادية؟)

**الأدوات اللي بنستخدمها (toolsIntegration):** (إذا في أدوات معينة بتساعدني أتعلم "{{{skillBehavior}}}", اشرحولي كيف بنقدر نستخدمها بالأشياء اللي بنعملها كل يوم. أو إذا مافي أدوات معينة، شو ممكن نستخدم أشياء بسيطة من البيت؟)

**نصايح إلكم (generalTips):** (يا أحسن أهل بالدنيا، عشان هالشغلات تكون ممتعة ومفيدة إلي، اتذكروا إني لساتني صغير (عمري {{{ageRange}}}) وبحب التشجيع واللعب. شو كمان شغلات ممكن تعملوها عشان يكون كل إشي سهل وحلو إلي وإلكم؟)

تأكدوا إنه الجواب يكون كامل ومفصل لكل قسم، وكأنه طالع مني أنا، الطفل {{{childName}}}، وبحكي أردني.
`,
});

const analyzeSkillFlow = ai?.defineFlow(
  {
    name: 'analyzeSkillFlow',
    inputSchema: SkillAnalysisInputSchema,
    outputSchema: SkillAnalysisOutputSchema,
  },
  async (input) => {
    if (!prompt) {
      throw new Error("AI prompt is not available");
    }
    const {output} = await prompt(input);
    if (!output) {
        throw new Error("لم يتمكن الذكاء الاصطناعي من إنشاء تحليل للمهارة.");
    }
    return output;
  }
);

