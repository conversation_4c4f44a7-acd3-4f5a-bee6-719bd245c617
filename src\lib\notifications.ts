import { prisma } from '@/lib/prisma';
import { NotificationType } from '@/lib/types';
import { differenceInDays, parseISO } from 'date-fns';
import { calculateAge } from '@/lib/utils';

export interface CreateNotificationParams {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  childId?: string;
  assessmentId?: string;
  planId?: string;
  sessionNoteId?: string;
  metadata?: Record<string, any>;
}

export interface NotificationTemplate {
  title: string;
  message: string;
}

// Notification templates
export const notificationTemplates = {
  assessment_overdue: (childName: string, daysSince: number): NotificationTemplate => ({
    title: `تقييم متأخر: ${childName}`,
    message: `آخر تقييم للطفل ${childName} كان منذ ${daysSince} يومًا. يُنصح بإجراء تقييم جديد.`,
  }),
  
  assessment_due_soon: (childName: string, daysSince: number): NotificationTemplate => ({
    title: `تقييم مطلوب قريبًا: ${childName}`,
    message: `آخر تقييم للطفل ${childName} كان منذ ${daysSince} يومًا. قد يحتاج لتقييم جديد قريبًا.`,
  }),
  
  service_complete: (childName: string, age: string): NotificationTemplate => ({
    title: `اكتمال الخدمة: ${childName}`,
    message: `الطفل ${childName} بلغ ${age} وقد اكتملت خدماته في البرنامج.`,
  }),
  
  plan_created: (childName: string, planType: string): NotificationTemplate => ({
    title: `خطة جديدة: ${childName}`,
    message: `تم إنشاء ${planType} جديدة للطفل ${childName}.`,
  }),
  
  session_note_added: (childName: string, date: string): NotificationTemplate => ({
    title: `محضر جلسة جديد: ${childName}`,
    message: `تم إضافة محضر جلسة جديد للطفل ${childName} بتاريخ ${date}.`,
  }),
  
  system: (title: string, message: string): NotificationTemplate => ({
    title,
    message,
  }),
};

// Create a notification
export async function createNotification(params: CreateNotificationParams) {
  try {
    const notification = await prisma.notification.create({
      data: params,
    });
    
    return notification;
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
}

// Create multiple notifications
export async function createNotifications(notifications: CreateNotificationParams[]) {
  try {
    const result = await prisma.notification.createMany({
      data: notifications,
    });
    
    return result;
  } catch (error) {
    console.error('Error creating notifications:', error);
    throw error;
  }
}

// Mark notification as read
export async function markNotificationAsRead(notificationId: string, userId: string) {
  try {
    const notification = await prisma.notification.updateMany({
      where: {
        id: notificationId,
        userId,
      },
      data: {
        isRead: true,
        readAt: new Date(),
      },
    });
    
    return notification;
  } catch (error) {
    console.error('Error marking notification as read:', error);
    throw error;
  }
}

// Mark all notifications as read for a user
export async function markAllNotificationsAsRead(userId: string) {
  try {
    const result = await prisma.notification.updateMany({
      where: {
        userId,
        isRead: false,
      },
      data: {
        isRead: true,
        readAt: new Date(),
      },
    });
    
    return result;
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    throw error;
  }
}

// Delete old notifications (cleanup)
export async function deleteOldNotifications(daysOld: number = 30) {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);
    
    const result = await prisma.notification.deleteMany({
      where: {
        createdAt: {
          lt: cutoffDate,
        },
        isRead: true,
      },
    });
    
    return result;
  } catch (error) {
    console.error('Error deleting old notifications:', error);
    throw error;
  }
}

// Get notification count for a user
export async function getNotificationCount(userId: string, unreadOnly: boolean = false) {
  try {
    const where: any = { userId };
    
    if (unreadOnly) {
      where.isRead = false;
    }
    
    const count = await prisma.notification.count({ where });
    return count;
  } catch (error) {
    console.error('Error getting notification count:', error);
    throw error;
  }
}

// Generate assessment notifications for all users
export async function generateAssessmentNotifications() {
  try {
    const users = await prisma.user.findMany({
      select: { id: true },
    });
    
    const children = await prisma.child.findMany({
      where: { isDeleted: { not: true } },
      include: {
        assessments: {
          select: { id: true, assessmentDate: true },
          orderBy: { assessmentDate: 'desc' },
          take: 1,
        },
      },
    });
    
    const notifications: CreateNotificationParams[] = [];
    const today = new Date();
    
    for (const user of users) {
      for (const child of children) {
        // Skip if child doesn't have a valid birthDate
        if (!child.birthDate) continue;
        
        try {
          // Skip if child doesn't have a valid birthDate
          if (!child.birthDate) continue;

          const age = calculateAge(child.birthDate.toISOString());

          // Skip if service is complete (6+ years)
          if (age.years >= 6) continue;
          
          const lastAssessment = child.assessments[0];
          
          if (lastAssessment) {
            const daysSinceLastAssessment = differenceInDays(today, new Date(lastAssessment.assessmentDate));
            
            // Check if notification already exists for this child and user
            const existingNotification = await prisma.notification.findFirst({
              where: {
                userId: user.id,
                childId: child.id,
                type: {
                  in: ['assessment_overdue', 'assessment_due_soon'],
                },
                createdAt: {
                  gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
                },
              },
            });
            
            if (!existingNotification) {
              if (daysSinceLastAssessment > 90) {
                // Overdue notification
                const template = notificationTemplates.assessment_overdue(child.name, daysSinceLastAssessment);
                notifications.push({
                  userId: user.id,
                  type: 'assessment_overdue',
                  title: template.title,
                  message: template.message,
                  childId: child.id,
                  assessmentId: lastAssessment.id,
                  metadata: {
                    daysSinceLastAssessment,
                    urgent: daysSinceLastAssessment > 120,
                  },
                });
              } else if (daysSinceLastAssessment > 75) {
                // Due soon notification
                const template = notificationTemplates.assessment_due_soon(child.name, daysSinceLastAssessment);
                notifications.push({
                  userId: user.id,
                  type: 'assessment_due_soon',
                  title: template.title,
                  message: template.message,
                  childId: child.id,
                  assessmentId: lastAssessment.id,
                  metadata: {
                    daysSinceLastAssessment,
                    urgent: false,
                  },
                });
              }
            }
          }
        } catch (error) {
          console.error(`Error processing child ${child.id}:`, error);
          continue;
        }
      }
    }
    
    if (notifications.length > 0) {
      await createNotifications(notifications);
    }
    
    return { created: notifications.length };
  } catch (error) {
    console.error('Error generating assessment notifications:', error);
    throw error;
  }
}
