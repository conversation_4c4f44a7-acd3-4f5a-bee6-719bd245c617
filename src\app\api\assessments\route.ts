import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for assessment data
const assessmentSchema = z.object({
  childId: z.string().min(1, 'Child ID is required'),
  assessmentDate: z.string().datetime('Invalid assessment date'),
  assessedSkills: z.array(z.any()).min(1, 'At least one skill must be assessed'),
  baselines: z.record(z.string()).optional(),
  ceilings: z.record(z.string()).optional(),
});

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const childId = searchParams.get('childId');

    let whereClause = {};
    if (childId) {
      whereClause = { childId };
    }

    const assessments = await prisma.assessment.findMany({
      where: whereClause,
      include: {
        child: {
          select: {
            id: true,
            name: true,
            childIdNumber: true,
          },
        },
        _count: {
          select: {
            learningPlans: true,
            comprehensiveReports: true,
          },
        },
      },
      orderBy: { assessmentDate: 'desc' },
    });

    return NextResponse.json(assessments);
  } catch (error) {
    console.error('Error fetching assessments:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only certain roles can create assessments
    const allowedRoles = ['super_admin', 'eiu_manager', 'case_manager', 'specialist'];
    if (!allowedRoles.includes(session.user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = assessmentSchema.parse(body);

    // Verify child exists
    const child = await prisma.child.findUnique({
      where: { id: validatedData.childId },
    });

    if (!child) {
      return NextResponse.json(
        { error: 'Child not found' },
        { status: 404 }
      );
    }

    const assessment = await prisma.assessment.create({
      data: {
        ...validatedData,
        assessmentDate: new Date(validatedData.assessmentDate),
        assessedSkills: validatedData.assessedSkills as any,
        baselines: validatedData.baselines as any,
        ceilings: validatedData.ceilings as any,
      },
      include: {
        child: {
          select: {
            id: true,
            name: true,
            childIdNumber: true,
          },
        },
        _count: {
          select: {
            learningPlans: true,
            comprehensiveReports: true,
          },
        },
      },
    });

    return NextResponse.json(assessment, { status: 201 });
  } catch (error) {
    console.error('Error creating assessment:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
