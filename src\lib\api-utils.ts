import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { z } from 'zod';

export async function requireAuth() {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    throw new ApiError('Unauthorized', 401);
  }
  
  return session;
}

export function requireRole(session: any, allowedRoles: string[]) {
  if (!allowedRoles.includes(session.user.role)) {
    throw new ApiError('Insufficient permissions', 403);
  }
}

export class ApiError extends Error {
  constructor(public message: string, public status: number) {
    super(message);
    this.name = 'ApiError';
  }
}

export function handleApiError(error: unknown) {
  console.error('API Error:', error);
  
  if (error instanceof ApiError) {
    return NextResponse.json(
      { error: error.message },
      { status: error.status }
    );
  }
  
  if (error instanceof z.ZodError) {
    return NextResponse.json(
      { error: 'Validation failed', details: error.errors },
      { status: 400 }
    );
  }
  
  return NextResponse.json(
    { error: 'Internal server error' },
    { status: 500 }
  );
}

export async function withAuth<T>(
  handler: (session: any) => Promise<T>,
  requiredRoles?: string[]
): Promise<T> {
  const session = await requireAuth();
  
  if (requiredRoles) {
    requireRole(session, requiredRoles);
  }
  
  return handler(session);
}

// Common validation schemas
export const commonSchemas = {
  id: z.string().min(1, 'ID is required'),
  email: z.string().email('Invalid email format'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
  date: z.string().datetime('Invalid date format'),
  optionalString: z.string().optional(),
  optionalUrl: z.string().url().optional(),
};
