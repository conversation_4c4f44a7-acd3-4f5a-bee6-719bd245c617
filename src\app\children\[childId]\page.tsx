import { use } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import type { Child, Assessment } from '@/lib/types';
import ChildProfileClient from '@/components/children/ChildProfileClient';

export default function ChildPage({ params }: { params: Promise<{ childId: string }> }) {
  const resolvedParams = use(params);
  return <ChildProfileClient childId={resolvedParams.childId} />;
}
