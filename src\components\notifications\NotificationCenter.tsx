"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Check<PERSON>heck, Loader2 } from 'lucide-react';
import { NotificationItem } from './NotificationItem';
import { Notification } from '@/lib/types';

interface NotificationCenterProps {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  onMarkAllAsRead: () => void;
  onClose: () => void;
}

export function NotificationCenter({
  notifications,
  unreadCount,
  isLoading,
  onMarkAllAsRead,
  onClose,
}: NotificationCenterProps) {
  return (
    <Card className="border-0 shadow-lg">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">الإشعارات</CardTitle>
            <CardDescription>
              {unreadCount > 0 
                ? `${unreadCount} إشعار غير مقروء` 
                : 'لا توجد إشعارات جديدة'
              }
            </CardDescription>
          </div>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onMarkAllAsRead}
              disabled={isLoading}
              className="text-xs"
            >
              {isLoading ? (
                <Loader2 className="h-3 w-3 animate-spin ml-1" />
              ) : (
                <CheckCheck className="h-3 w-3 ml-1" />
              )}
              تحديد الكل كمقروء
            </Button>
          )}
        </div>
      </CardHeader>
      
      <Separator />
      
      <CardContent className="p-0">
        {isLoading ? (
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="mr-2">جاري التحميل...</span>
          </div>
        ) : notifications.length === 0 ? (
          <div className="p-8 text-center text-sm text-muted-foreground">
            لا توجد إشعارات
          </div>
        ) : (
          <ScrollArea className="max-h-80">
            <div className="space-y-0">
              {notifications.map((notification, index) => (
                <div key={notification.id}>
                  <NotificationItem 
                    notification={notification}
                    onClose={onClose}
                  />
                  {index < notifications.length - 1 && <Separator />}
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
      
      {notifications.length > 5 && (
        <>
          <Separator />
          <div className="p-3 text-center">
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-xs text-muted-foreground"
              onClick={onClose}
            >
              عرض جميع الإشعارات
            </Button>
          </div>
        </>
      )}
    </Card>
  );
}
