import { cn } from "@/lib/utils"
import { Loader2 } from "lucide-react"

interface LoadingProps {
  className?: string
  size?: "sm" | "md" | "lg"
  variant?: "spinner" | "dots" | "pulse"
  text?: string
}

const Loading = ({ 
  className, 
  size = "md", 
  variant = "spinner", 
  text 
}: LoadingProps) => {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-8 w-8", 
    lg: "h-12 w-12"
  }

  const textSizeClasses = {
    sm: "text-xs",
    md: "text-sm",
    lg: "text-base"
  }

  if (variant === "spinner") {
    return (
      <div className={cn("flex flex-col items-center justify-center py-8", className)}>
        <Loader2 className={cn("animate-spin text-primary", sizeClasses[size])} />
        {text && (
          <p className={cn("text-muted-foreground mt-4", textSizeClasses[size])}>
            {text}
          </p>
        )}
      </div>
    )
  }

  if (variant === "dots") {
    return (
      <div className={cn("flex flex-col items-center justify-center py-8", className)}>
        <div className="flex space-x-1">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className={cn(
                "bg-primary rounded-full animate-pulse",
                size === "sm" ? "h-2 w-2" : size === "md" ? "h-3 w-3" : "h-4 w-4"
              )}
              style={{
                animationDelay: `${i * 0.2}s`,
                animationDuration: "1s"
              }}
            />
          ))}
        </div>
        {text && (
          <p className={cn("text-muted-foreground mt-4", textSizeClasses[size])}>
            {text}
          </p>
        )}
      </div>
    )
  }

  // pulse variant
  return (
    <div className={cn("flex flex-col items-center justify-center py-8", className)}>
      <div className={cn(
        "bg-primary/20 rounded-full animate-pulse",
        sizeClasses[size]
      )} />
      {text && (
        <p className={cn("text-muted-foreground mt-4", textSizeClasses[size])}>
          {text}
        </p>
      )}
    </div>
  )
}

export { Loading } 