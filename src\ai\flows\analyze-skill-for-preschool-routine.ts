'use server';
/**
 * @fileOverview Provides AI-driven analysis for integrating a child's skill into preschool/kindergarten routines.
 *
 * - analyzeSkillForPreschoolRoutine - Function to generate routine integration suggestions for a skill in a preschool setting.
 * - PreschoolSkillAnalysisInput - Input type for the analysis.
 * - PreschoolSkillAnalysisOutput - Output type for the analysis.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const PreschoolSkillAnalysisInputSchema = z.object({
  skillBehavior: z.string().describe('وصف سلوك المهارة أو الهدف المطلوب تحليله.'),
  ageRange: z.string().describe('الفئة العمرية للطفل المستهدف بالمهارة.'),
  childName: z.string().describe('اسم الطفل.'),
});
export type PreschoolSkillAnalysisInput = z.infer<typeof PreschoolSkillAnalysisInputSchema>;

const PreschoolSkillAnalysisOutputSchema = z.object({
  arrivalTime: z.string().describe("اقتراحات عملية ومفصلة لدمج المهارة خلال وقت الوصول إلى الروضة/الحضانة."),
  morningCircle: z.string().describe("اقتراحات عملية ومفصلة لدمج المهارة خلال أنشطة الدائرة الصباحية."),
  activityTransition: z.string().describe("اقتراحات عملية ومفصلة لدمج المهارة خلال أوقات الانتقال بين الأنشطة."),
  learningCenters: z.string().describe("اقتراحات عملية ومفصلة لدمج المهارة في المراكز/أركان التعلم المختلفة."),
  outdoorPlay: z.string().describe("اقتراحات عملية ومفصلة لدمج المهارة أثناء اللعب في الساحة الخارجية."),
  preschoolBathroom: z.string().describe("اقتراحات عملية ومفصلة لدمج المهارة في وقت استخدام الحمام داخل الروضة/الحضانة."),
  preschoolSnackTime: z.string().describe("اقتراحات عملية ومفصلة لدمج المهارة في وقت الوجبة الخفيفة/الغداء في الروضة/الحضانة."),
  storyTime: z.string().describe("اقتراحات عملية ومفصلة لدمج المهارة خلال وقت القصة."),
  departureTime: z.string().describe("اقتراحات عملية ومفصلة لدمج المهارة خلال وقت المغادرة من الروضة/الحضانة."),
  preschoolToolsIntegration: z.string().describe("شرح لكيفية استخدام الأدوات المذكورة (إن وجدت في بيانات المهارة الأصلية) في هذه الأنشطة الروتينية أو اقتراح أدوات بسيطة مناسبة للروضة."),
  teacherGeneralTips: z.string().describe("نصائح عامة للمعلمين لضمان قابلية تطبيق الاقتراحات بشكل واقعي وممتع في بيئة جماعية."),
});
export type PreschoolSkillAnalysisOutput = z.infer<typeof PreschoolSkillAnalysisOutputSchema>;

export async function analyzeSkillForPreschoolRoutine(
  input: PreschoolSkillAnalysisInput
): Promise<PreschoolSkillAnalysisOutput> {
  // Check if AI is disabled
  if (!ai) {
    return {
      arrivalTime: "ميزة التحليل التلقائي غير متاحة حاليًا. يرجى إعداد مفتاح Google AI API.",
      morningCircle: "ميزة التحليل التلقائي غير متاحة حاليًا. يرجى إعداد مفتاح Google AI API.",
      activityTransition: "ميزة التحليل التلقائي غير متاحة حاليًا. يرجى إعداد مفتاح Google AI API.",
      learningCenters: "ميزة التحليل التلقائي غير متاحة حاليًا. يرجى إعداد مفتاح Google AI API.",
      outdoorPlay: "ميزة التحليل التلقائي غير متاحة حاليًا. يرجى إعداد مفتاح Google AI API.",
      preschoolBathroom: "ميزة التحليل التلقائي غير متاحة حاليًا. يرجى إعداد مفتاح Google AI API.",
      preschoolSnackTime: "ميزة التحليل التلقائي غير متاحة حاليًا. يرجى إعداد مفتاح Google AI API.",
      storyTime: "ميزة التحليل التلقائي غير متاحة حاليًا. يرجى إعداد مفتاح Google AI API.",
      departureTime: "ميزة التحليل التلقائي غير متاحة حاليًا. يرجى إعداد مفتاح Google AI API.",
      preschoolToolsIntegration: "ميزة التحليل التلقائي غير متاحة حاليًا. يرجى إعداد مفتاح Google AI API.",
      teacherGeneralTips: "ميزة التحليل التلقائي غير متاحة حاليًا. يرجى الاستعانة بأخصائي تطوير الطفولة المبكرة."
    };
  }
  
  return analyzePreschoolSkillFlow(input);
}

// Only define AI flows if AI is available
const prompt = ai?.definePrompt({
  name: 'analyzeSkillForPreschoolRoutinePrompt',
  input: {schema: PreschoolSkillAnalysisInputSchema},
  output: {schema: PreschoolSkillAnalysisOutputSchema},
  prompt: `أنت خبير في تنمية الطفولة المبكرة وبرنامج بورتيج، متخصص في مساعدة معلمي الحضانات ورياض الأطفال على دمج الأهداف التعليمية للأطفال في أنشطتهم اليومية داخل بيئة الروضة/الحضانة بشكل عملي ومبتكر.

الطفل: {{{childName}}}
المهارة المستهدفة (الهدف): "{{{skillBehavior}}}"
الفئة العمرية للمهارة: {{{ageRange}}}

الرجاء تقديم تحليل مفصل واقتراحات عملية حول كيفية دمج هذه المهارة في الروتين اليومي للطفل ضمن بيئة الحضانة أو الروضة. يجب أن تكون الاقتراحات قابلة للتطبيق بسهولة من قبل المعلمين.

حلل الهدف وقدم اقتراحات محددة لكل من الأوقات والأنشطة التالية في الروضة/الحضانة:
1.  **وقت الوصول:** (كيف يمكن استقبال الطفل ودمج المهارة خلال اللحظات الأولى في الروضة؟)
2.  **الدائرة الصباحية:** (كيف يمكن دمج المهارة أثناء أنشطة الدائرة الصباحية مثل الأناشيد، عرض التاريخ، الطقس، أو مناقشة موضوع اليوم؟)
3.  **الانتقال بين الأنشطة:** (كيف يمكن استغلال أوقات الانتقال لتعزيز المهارة بشكل سلس؟)
4.  **المراكز/أركان التعلم:** (اقترح أنشطة محددة في أركان التعلم المختلفة - مثل ركن البناء، ركن القراءة، ركن الفن، ركن الاكتشاف - التي تعزز هذه المهارة)
5.  **الساحة الخارجية:** (كيف يمكن تعزيز المهارة أثناء اللعب في الخارج؟)
6.  **وقت الحمام (في الروضة):** (كيف يمكن دمج المهارة أثناء روتين استخدام الحمام في الروضة؟ سمِ هذا القسم preschoolBathroom)
7.  **الوجبة الخفيفة/الغداء (في الروضة):** (كيف يمكن ممارسة أو تعزيز المهارة أثناء تناول الوجبات في الروضة؟ سمِ هذا القسم preschoolSnackTime)
8.  **وقت القصة:** (هل هناك طرق لدمج المهارة أثناء سرد القصص أو الأنشطة المتعلقة بها؟)
9.  **المغادرة:** (كيف يمكن تعزيز المهارة أو تلخيص ما تم تعلمه خلال الاستعداد للمغادرة؟)

بالنسبة لـ "استخدام الأدوات في الروضة": اشرح كيف يمكن استخدام الأدوات المذكورة عادةً لهذه المهارة (حتى لو لم تُذكر صراحةً في الإدخال الحالي، يمكنك اقتراح أدوات شائعة أو بسيطة متوفرة في الروضة إذا كانت مناسبة) في هذه الأنشطة الروتينية. سمِ هذا القسم preschoolToolsIntegration.

قدم "نصائح عامة للتطبيق للمعلمين": لضمان أن تكون الاقتراحات ممتعة وقابلة للتطبيق بشكل واقعي في بيئة جماعية، مع مراعاة الفئة العمرية. سمِ هذا القسم teacherGeneralTips.

تأكد من أن الإجابة تكون شاملة ومفصلة لكل قسم.
`,
});

const analyzePreschoolSkillFlow = ai?.defineFlow(
  {
    name: 'analyzePreschoolSkillFlow',
    inputSchema: PreschoolSkillAnalysisInputSchema,
    outputSchema: PreschoolSkillAnalysisOutputSchema,
  },
  async (input) => {
    if (!prompt) {
      throw new Error("AI prompt is not available");
    }
    const {output} = await prompt(input);
    if (!output) {
        throw new Error("لم يتمكن الذكاء الاصطناعي من إنشاء تحليل للمهارة في سياق الروضة.");
    }
    return output;
  }
);
