import nodemailer from 'nodemailer';
import { prisma } from '@/lib/prisma';
import { NotificationType } from '@/lib/types';

export interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

// Email templates
export const emailTemplates = {
  assessment_overdue: (childName: string, daysSince: number): EmailTemplate => ({
    subject: `تقييم متأخر: ${childName}`,
    html: `
      <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #dc2626;">تقييم متأخر</h2>
        <p>السلام عليكم ورحمة الله وبركاته،</p>
        <p>نود تذكيركم بأن آخر تقييم للطفل <strong>${childName}</strong> كان منذ <strong>${daysSince} يومًا</strong>.</p>
        <p>يُنصح بإجراء تقييم جديد في أقرب وقت ممكن لضمان متابعة تطور الطفل بشكل مناسب.</p>
        <div style="background-color: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 16px; margin: 16px 0;">
          <p style="margin: 0; color: #991b1b;">
            <strong>تنبيه:</strong> التقييم متأخر عن الموعد المحدد. يرجى اتخاذ الإجراء اللازم.
          </p>
        </div>
        <p>شكرًا لكم على اهتمامكم ومتابعتكم.</p>
        <p>مع أطيب التحيات،<br>فريق البرنامج</p>
      </div>
    `,
    text: `تقييم متأخر: ${childName}\n\nآخر تقييم للطفل ${childName} كان منذ ${daysSince} يومًا. يُنصح بإجراء تقييم جديد في أقرب وقت ممكن.`,
  }),

  assessment_due_soon: (childName: string, daysSince: number): EmailTemplate => ({
    subject: `تذكير: تقييم مطلوب قريبًا - ${childName}`,
    html: `
      <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">تذكير بالتقييم</h2>
        <p>السلام عليكم ورحمة الله وبركاته،</p>
        <p>نود تذكيركم بأن آخر تقييم للطفل <strong>${childName}</strong> كان منذ <strong>${daysSince} يومًا</strong>.</p>
        <p>قد يحتاج الطفل لتقييم جديد قريبًا وفقًا للجدول الزمني المحدد.</p>
        <div style="background-color: #eff6ff; border: 1px solid #bfdbfe; border-radius: 8px; padding: 16px; margin: 16px 0;">
          <p style="margin: 0; color: #1e40af;">
            <strong>تذكير:</strong> يُنصح بجدولة التقييم التالي قريبًا.
          </p>
        </div>
        <p>شكرًا لكم على اهتمامكم ومتابعتكم.</p>
        <p>مع أطيب التحيات،<br>فريق البرنامج</p>
      </div>
    `,
    text: `تذكير: تقييم مطلوب قريبًا - ${childName}\n\nآخر تقييم للطفل ${childName} كان منذ ${daysSince} يومًا. قد يحتاج لتقييم جديد قريبًا.`,
  }),

  service_complete: (childName: string, age: string): EmailTemplate => ({
    subject: `اكتمال الخدمة: ${childName}`,
    html: `
      <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #059669;">اكتمال الخدمة</h2>
        <p>السلام عليكم ورحمة الله وبركاته،</p>
        <p>نود إعلامكم بأن الطفل <strong>${childName}</strong> قد بلغ <strong>${age}</strong> وبذلك تكون خدماته في البرنامج قد اكتملت.</p>
        <p>نشكركم على ثقتكم بنا ونتمنى للطفل مستقبلاً مشرقًا.</p>
        <div style="background-color: #ecfdf5; border: 1px solid #a7f3d0; border-radius: 8px; padding: 16px; margin: 16px 0;">
          <p style="margin: 0; color: #065f46;">
            <strong>تهانينا!</strong> تم إكمال البرنامج بنجاح.
          </p>
        </div>
        <p>مع أطيب التحيات،<br>فريق البرنامج</p>
      </div>
    `,
    text: `اكتمال الخدمة: ${childName}\n\nالطفل ${childName} بلغ ${age} وقد اكتملت خدماته في البرنامج.`,
  }),

  test_email: (recipientName: string): EmailTemplate => ({
    subject: 'اختبار إعدادات البريد الإلكتروني',
    html: `
      <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">اختبار البريد الإلكتروني</h2>
        <p>مرحبًا ${recipientName}،</p>
        <p>هذه رسالة اختبار للتأكد من أن إعدادات البريد الإلكتروني تعمل بشكل صحيح.</p>
        <div style="background-color: #ecfdf5; border: 1px solid #a7f3d0; border-radius: 8px; padding: 16px; margin: 16px 0;">
          <p style="margin: 0; color: #065f46;">
            <strong>نجح الاختبار!</strong> إعدادات البريد الإلكتروني تعمل بشكل صحيح.
          </p>
        </div>
        <p>تاريخ الإرسال: ${new Date().toLocaleString('ar-SA')}</p>
        <p>مع أطيب التحيات،<br>فريق البرنامج</p>
      </div>
    `,
    text: `اختبار البريد الإلكتروني\n\nمرحبًا ${recipientName}، هذه رسالة اختبار للتأكد من أن إعدادات البريد الإلكتروني تعمل بشكل صحيح.`,
  }),
};

// Get email transporter
async function getEmailTransporter() {
  const settings = await prisma.emailSettings.findFirst();
  
  if (!settings || !settings.isEnabled) {
    throw new Error('Email service is not enabled');
  }

  if (!settings.smtpHost || !settings.smtpUser || !settings.smtpPassword) {
    throw new Error('Email settings are incomplete');
  }

  return nodemailer.createTransporter({
    host: settings.smtpHost,
    port: settings.smtpPort || 587,
    secure: settings.smtpSecure || false,
    auth: {
      user: settings.smtpUser,
      pass: settings.smtpPassword,
    },
  });
}

// Send email
export async function sendEmail(
  to: string,
  template: EmailTemplate,
  fromEmail?: string,
  fromName?: string
) {
  try {
    const transporter = await getEmailTransporter();
    const settings = await prisma.emailSettings.findFirst();
    
    const mailOptions = {
      from: `${fromName || settings?.fromName || 'نظام البرنامج'} <${fromEmail || settings?.fromEmail || settings?.smtpUser}>`,
      to,
      subject: template.subject,
      text: template.text,
      html: template.html,
    };

    const result = await transporter.sendMail(mailOptions);
    return result;
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
}

// Send test email
export async function sendTestEmail(to: string, recipientName: string) {
  const template = emailTemplates.test_email(recipientName);
  return await sendEmail(to, template);
}

// Send notification email
export async function sendNotificationEmail(
  to: string,
  type: NotificationType,
  data: any
) {
  let template: EmailTemplate;

  switch (type) {
    case 'assessment_overdue':
      template = emailTemplates.assessment_overdue(data.childName, data.daysSince);
      break;
    case 'assessment_due_soon':
      template = emailTemplates.assessment_due_soon(data.childName, data.daysSince);
      break;
    case 'service_complete':
      template = emailTemplates.service_complete(data.childName, data.age);
      break;
    default:
      throw new Error(`Email template not found for notification type: ${type}`);
  }

  return await sendEmail(to, template);
}
