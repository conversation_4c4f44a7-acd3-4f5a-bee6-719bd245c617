import { NextResponse } from 'next/server';
import { generateAssessmentNotifications, deleteOldNotifications } from '@/lib/notifications';

// GET /api/cron/notifications - Scheduled task to generate notifications
export async function GET(request: Request) {
  try {
    // Verify this is a legitimate cron request (you can add authentication here)
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET || 'your-cron-secret';
    
    if (authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('Running scheduled notification generation...');

    // Generate assessment notifications
    const assessmentResult = await generateAssessmentNotifications();
    
    // Clean up old notifications (older than 30 days)
    const cleanupResult = await deleteOldNotifications(30);

    console.log(`Generated ${assessmentResult.created} notifications, cleaned up ${cleanupResult.count} old notifications`);

    return NextResponse.json({
      success: true,
      assessmentNotifications: assessmentResult.created,
      cleanedUpNotifications: cleanupResult.count,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error in notification cron job:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
