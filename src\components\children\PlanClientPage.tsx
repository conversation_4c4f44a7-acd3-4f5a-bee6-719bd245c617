"use client";

import React, { useState, useEffect } from 'react';
import { PORTAGE_CHECKLIST_DATA } from '@/lib/constants';
import type { Child, Assessment, PortageSkillItem } from '@/lib/types';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight, FileText } from 'lucide-react';
import ComprehensiveReportGeneratorForm from '@/components/learning-plan/LearningPlanGeneratorForm';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useChildren, useAssessments } from '@/hooks/use-storage';
import type { AnalyzableItemType } from '@/components/assessment/AnalyzableSkillItem';

interface PlanClientPageProps {
  childId: string;
}

// --- Helper functions adapted from the original page ---
const getSkillDetails = (skillId: string): (PortageSkillItem & { dimensionName: string; subCategoryName: string; }) | null => {
  for (const dimension of PORTAGE_CHECKLIST_DATA) {
    for (const subCategory of dimension.subCategories) {
      const skill = subCategory.skills.find(s => s.id === skillId);
      if (skill) return { ...skill, dimensionName: dimension.name, subCategoryName: subCategory.name };
    }
  }
  return null;
};

interface AnalysisResult {
  baselineSkill: AnalyzableItemType | null;
  ceilingSkill: AnalyzableItemType | null;
  teachingRangeSkills: AnalyzableItemType[];
}

interface SubCategoryAnalysisData extends AnalysisResult {
  subCategoryName: string;
  skillsCount: number;
}

export interface DimensionAnalysisData {
  dimensionName: string;
  subCategories: SubCategoryAnalysisData[];
}

function calculateAnalysisForSubCategory(skillsInSubCategory: AnalyzableItemType[]): AnalysisResult {
  const sortedSkills = [...skillsInSubCategory].sort((a, b) => {
    const numA = parseInt(a.itemNumber, 10);
    const numB = parseInt(b.itemNumber, 10);
    return numA - numB;
  });

  let baselineSkill: AnalyzableItemType | null = null;
  let ceilingSkill: AnalyzableItemType | null = null;
  const teachingRangeSkills: AnalyzableItemType[] = [];

  for (let i = 0; i <= sortedSkills.length - 3; i++) {
    if (
      sortedSkills[i].status === 'yes' &&
      sortedSkills[i + 1].status === 'yes' &&
      sortedSkills[i + 2].status === 'yes'
    ) {
      baselineSkill = sortedSkills[i + 2];
      break;
    }
  }
  if (!baselineSkill) {
    const firstYesSkill = sortedSkills.find(skill => skill.status === 'yes');
    if (firstYesSkill) {
      baselineSkill = firstYesSkill;
    }
  }

  for (let i = 0; i <= sortedSkills.length - 3; i++) {
    if (
      sortedSkills[i].status === 'no' &&
      sortedSkills[i + 1].status === 'no' &&
      sortedSkills[i + 2].status === 'no'
    ) {
      ceilingSkill = sortedSkills[i];
      break;
    }
  }
  if (!ceilingSkill) {
    let highestNoSkill: AnalyzableItemType | null = null;
    for (let i = sortedSkills.length - 1; i >= 0; i--) {
        if (sortedSkills[i].status === 'no') {
            highestNoSkill = sortedSkills[i];
            break;
        }
    }
    if (highestNoSkill) {
        ceilingSkill = highestNoSkill;
    }
  }

  let teachingStartIndex = 0;
  if (baselineSkill) {
    const baselineIdx = sortedSkills.findIndex(s => s.skillId === baselineSkill!.skillId);
    if (baselineIdx !== -1) {
      teachingStartIndex = baselineIdx + 1;
    }
  }

  for (let i = teachingStartIndex; i < sortedSkills.length; i++) {
    const skill = sortedSkills[i];
    if (ceilingSkill && parseInt(skill.itemNumber, 10) > parseInt(ceilingSkill.itemNumber, 10)) {
      break;
    }
    if (skill.status === 'no' || skill.status === 'unclear') {
      teachingRangeSkills.push(skill);
    }
  }
  
  return { baselineSkill, ceilingSkill, teachingRangeSkills };
}

export default function PlanClientPage({ childId }: PlanClientPageProps) {
  const { getChild, loading: childrenLoading } = useChildren();
  const { assessments, loading: assessmentsLoading } = useAssessments(childId);
  
  const [child, setChild] = useState<Child | null>(null);
  const [latestAssessment, setLatestAssessment] = useState<Assessment | undefined>(undefined);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Wait for hooks to be ready
    if (childrenLoading || assessmentsLoading) {
      return;
    }

    const childData = getChild(childId);
    setChild(childData || null);

    // Get the latest assessment for this child
    if (assessments.length > 0) {
      const latest = assessments.sort((a, b) => new Date(b.assessmentDate).getTime() - new Date(a.assessmentDate).getTime())[0];
      setLatestAssessment(latest);
    } else {
      setLatestAssessment(undefined);
    }

    setLoading(false);
  }, [childId, getChild, assessments, childrenLoading, assessmentsLoading]);

  // Loading state
  if (loading || childrenLoading || assessmentsLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري تحميل بيانات الطفل...</p>
          </div>
        </div>
      </div>
    );
  }

  // Child not found
  if (!child) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h1 className="text-2xl font-semibold">لم يتم العثور على الطفل</h1>
        <Link href="/children">
          <Button variant="link">العودة إلى قائمة الأطفال</Button>
        </Link>
      </div>
    );
  }

  // Calculate structured analysis data
  let structuredAnalysisData: DimensionAnalysisData[] | undefined = undefined;

  if (latestAssessment) {
    structuredAnalysisData = PORTAGE_CHECKLIST_DATA.map(dimension => {
      const subCategoriesData: SubCategoryAnalysisData[] = dimension.subCategories.map(subCategory => {
        const skillsForSubCategory: AnalyzableItemType[] = [];
        latestAssessment.assessedSkills.forEach(assessedSkill => {
          const details = getSkillDetails(assessedSkill.skillId);
          if (details && details.dimensionName === dimension.name && details.subCategoryName === subCategory.name) {
            skillsForSubCategory.push({
              ...assessedSkill,
              ...details,
              skillId: assessedSkill.skillId,
            });
          }
        });
        skillsForSubCategory.sort((a, b) => parseInt(a.itemNumber, 10) - parseInt(b.itemNumber, 10));
        const analysis = calculateAnalysisForSubCategory(skillsForSubCategory);
        return {
          subCategoryName: subCategory.name,
          skillsCount: skillsForSubCategory.length,
          ...analysis,
        };
      });

      return {
        dimensionName: dimension.name,
        subCategories: subCategoriesData.filter(sc => sc.skillsCount > 0),
      };
    }).filter(dim => dim.subCategories.length > 0);
  }

  return (
    <div className="container mx-auto py-8">
      <Link href={`/children/${child.id}`} className="inline-flex items-center gap-2 text-sm text-primary hover:underline mb-4">
        العودة إلى ملف {child.name}
        <ArrowRight className="h-4 w-4" />
      </Link>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div className="flex items-center gap-3">
          <FileText className="h-10 w-10 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-primary">التقرير الشامل لـ {child.name}</h1>
            <p className="text-muted-foreground">إنشاء تقارير شاملة مع تحليل بيانات مدعومة بالذكاء الاصطناعي.</p>
          </div>
        </div>
      </div>

      <ComprehensiveReportGeneratorForm 
        child={child} 
        assessment={latestAssessment} 
        structuredAnalysisData={latestAssessment ? structuredAnalysisData : undefined} 
      />

      <Card className="mt-8 shadow-lg">
        <CardHeader>
          <CardTitle>التقارير السابقة</CardTitle>
          <CardDescription>مراجعة التقارير الشاملة التي تم إنشاؤها مسبقًا لـ {child.name}. (قريباً)</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">سيعرض هذا القسم التقارير الشاملة السابقة.</p>
        </CardContent>
      </Card>
    </div>
  );
} 