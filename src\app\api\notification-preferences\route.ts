import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for notification preferences
const notificationPreferencesSchema = z.object({
  enableInApp: z.boolean().optional(),
  assessmentReminders: z.boolean().optional(),
  serviceAlerts: z.boolean().optional(),
  planUpdates: z.boolean().optional(),
  sessionNoteAlerts: z.boolean().optional(),
  systemNotifications: z.boolean().optional(),
  enableEmail: z.boolean().optional(),
  emailAssessmentReminders: z.boolean().optional(),
  emailServiceAlerts: z.boolean().optional(),
  emailPlanUpdates: z.boolean().optional(),
  emailSessionNoteAlerts: z.boolean().optional(),
  emailSystemNotifications: z.boolean().optional(),
  emailFrequency: z.enum(['immediate', 'daily', 'weekly']).optional(),
});

// GET /api/notification-preferences - Get user's notification preferences
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    let preferences = await prisma.notificationPreferences.findUnique({
      where: { userId: session.user.id },
    });

    // Create default preferences if they don't exist
    if (!preferences) {
      preferences = await prisma.notificationPreferences.create({
        data: {
          userId: session.user.id,
          enableInApp: true,
          assessmentReminders: true,
          serviceAlerts: true,
          planUpdates: true,
          sessionNoteAlerts: true,
          systemNotifications: true,
          enableEmail: false,
          emailAssessmentReminders: false,
          emailServiceAlerts: false,
          emailPlanUpdates: false,
          emailSessionNoteAlerts: false,
          emailSystemNotifications: false,
          emailFrequency: 'immediate',
        },
      });
    }

    return NextResponse.json(preferences);
  } catch (error) {
    console.error('Error fetching notification preferences:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/notification-preferences - Update user's notification preferences
export async function PUT(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = notificationPreferencesSchema.parse(body);

    const preferences = await prisma.notificationPreferences.upsert({
      where: { userId: session.user.id },
      update: validatedData,
      create: {
        userId: session.user.id,
        enableInApp: true,
        assessmentReminders: true,
        serviceAlerts: true,
        planUpdates: true,
        sessionNoteAlerts: true,
        systemNotifications: true,
        enableEmail: false,
        emailAssessmentReminders: false,
        emailServiceAlerts: false,
        emailPlanUpdates: false,
        emailSessionNoteAlerts: false,
        emailSystemNotifications: false,
        emailFrequency: 'immediate',
        ...validatedData,
      },
    });

    return NextResponse.json(preferences);
  } catch (error) {
    console.error('Error updating notification preferences:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
