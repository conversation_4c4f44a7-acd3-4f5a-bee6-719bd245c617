'use client';

import { useState } from "react";
import { useRouter } from "next/navigation";
import { signIn } from "next-auth/react";
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "sonner";
import { z } from "zod";

// Validation schemas
const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
});

const registerSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
});

export function LoginForm({
  className,
  ...props
}: React.ComponentPropsWithoutRef<"form">) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [name, setName] = useState("");
  const [isRegistering, setIsRegistering] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      if (isRegistering) {
        // Validate registration data
        const validatedData = registerSchema.parse({ email, password, name });

        // Register user
        const response = await fetch('/api/auth/register', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(validatedData),
        });

        const data = await response.json();

        if (response.ok) {
          toast.success("تم إنشاء الحساب بنجاح! يرجى تسجيل الدخول");
          setIsRegistering(false);
          setPassword("");
          setName("");
        } else {
          toast.error(data.error || "حدث خطأ أثناء إنشاء الحساب");
        }
      } else {
        // Validate login data
        const validatedData = loginSchema.parse({ email, password });

        // Sign in with NextAuth
        const result = await signIn('credentials', {
          email: validatedData.email,
          password: validatedData.password,
          redirect: false,
        });

        if (result?.error) {
          toast.error("خطأ في البريد الإلكتروني أو كلمة المرور");
        } else if (result?.ok) {
          toast.success("تم تسجيل الدخول بنجاح");
          router.push("/");
          router.refresh();
        }
      }
    } catch (error) {
      console.error('Form submission error:', error);

      if (error instanceof z.ZodError) {
        const firstError = error.errors[0];
        toast.error(firstError.message);
      } else {
        toast.error(isRegistering ? "حدث خطأ أثناء إنشاء الحساب" : "حدث خطأ أثناء تسجيل الدخول");
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form className={cn("flex flex-col gap-6", className)} onSubmit={handleSubmit} {...props}>
      <div className="flex flex-col items-center gap-2 text-center">
        <h1 className="text-2xl font-bold arabic-text">{isRegistering ? "إنشاء حساب جديد" : "تسجيل الدخول"}</h1>
        <p className="text-balance text-sm text-muted-foreground arabic-text">
          {isRegistering ? "أدخل بياناتك أدناه لإنشاء حساب جديد" : "أدخل بريدك الإلكتروني أدناه لتسجيل الدخول إلى حسابك"}
        </p>
      </div>
      <div className="grid gap-6">
        {isRegistering && (
          <div className="grid gap-2">
            <Label htmlFor="name" className="arabic-text">الاسم</Label>
            <Input 
              id="name" 
              type="text" 
              placeholder="الاسم" 
              required 
              value={name}
              onChange={(e) => setName(e.target.value)}
              disabled={isLoading}
            />
          </div>
        )}
        <div className="grid gap-2">
          <Label htmlFor="email" className="arabic-text">البريد الإلكتروني</Label>
          <Input 
            id="email" 
            type="email" 
            placeholder="<EMAIL>" 
            required 
            dir="ltr"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={isLoading}
          />
        </div>
        <div className="grid gap-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="password" className="arabic-text">كلمة المرور</Label>
            {!isRegistering && (
              <a
                href="#"
                className="text-sm underline-offset-4 hover:underline arabic-text"
              >
                هل نسيت كلمة المرور؟
              </a>
            )}
          </div>
          <Input 
            id="password" 
            type="password" 
            required 
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            disabled={isLoading}
          />
        </div>
        <Button type="submit" className="w-full arabic-text" disabled={isLoading}>
          {isLoading ? (isRegistering ? "جاري إنشاء الحساب..." : "جاري تسجيل الدخول...") : (isRegistering ? "إنشاء حساب" : "تسجيل الدخول")}
        </Button>
        <Button variant="outline" type="button" className="w-full arabic-text" onClick={() => setIsRegistering(!isRegistering)} disabled={isLoading}>
          {isRegistering ? "العودة لتسجيل الدخول" : "إنشاء حساب جديد"}
        </Button>
      </div>
    </form>
  )
}
