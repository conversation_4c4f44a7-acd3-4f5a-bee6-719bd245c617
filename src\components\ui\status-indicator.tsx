import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Clock, 
  Pause, 
  Play,
  Loader2,
  Info
} from "lucide-react"

const statusIndicatorVariants = cva(
  "inline-flex items-center gap-2 font-medium",
  {
    variants: {
      status: {
        success: "text-green-700 dark:text-green-400",
        error: "text-red-700 dark:text-red-400",
        warning: "text-yellow-700 dark:text-yellow-400",
        info: "text-blue-700 dark:text-blue-400",
        pending: "text-gray-700 dark:text-gray-400",
        processing: "text-blue-700 dark:text-blue-400",
        paused: "text-orange-700 dark:text-orange-400",
        active: "text-green-700 dark:text-green-400",
      },
      size: {
        sm: "text-xs",
        md: "text-sm",
        lg: "text-base",
      },
    },
    defaultVariants: {
      status: "info",
      size: "md",
    },
  }
)

const getStatusIcon = (status: string, size: "sm" | "md" | "lg") => {
  const iconSize = size === "sm" ? "h-3 w-3" : size === "md" ? "h-4 w-4" : "h-5 w-5"
  
  switch (status) {
    case "success":
      return <CheckCircle className={iconSize} />
    case "error":
      return <XCircle className={iconSize} />
    case "warning":
      return <AlertTriangle className={iconSize} />
    case "info":
      return <Info className={iconSize} />
    case "pending":
      return <Clock className={iconSize} />
    case "processing":
      return <Loader2 className={cn(iconSize, "animate-spin")} />
    case "paused":
      return <Pause className={iconSize} />
    case "active":
      return <Play className={iconSize} />
    default:
      return <Info className={iconSize} />
  }
}

const getBadgeVariant = (status: string) => {
  switch (status) {
    case "success":
    case "active":
      return "default"
    case "error":
      return "destructive"
    case "warning":
    case "paused":
      return "secondary"
    case "info":
    case "pending":
    case "processing":
      return "outline"
    default:
      return "outline"
  }
}

export interface StatusIndicatorProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof statusIndicatorVariants> {
  status: "success" | "error" | "warning" | "info" | "pending" | "processing" | "paused" | "active"
  label: string
  showIcon?: boolean
  asBadge?: boolean
  pulse?: boolean
}

const StatusIndicator = React.forwardRef<HTMLDivElement, StatusIndicatorProps>(
  ({ className, status, size, label, showIcon = true, asBadge = false, pulse = false, ...props }, ref) => {
    if (asBadge) {
      return (
        <Badge
          variant={getBadgeVariant(status)}
          className={cn(
            "whitespace-nowrap",
            pulse && "animate-pulse",
            className
          )}
          {...props}
        >
          {showIcon && getStatusIcon(status, size || "md")}
          {label}
        </Badge>
      )
    }

    return (
      <div
        ref={ref}
        className={cn(
          statusIndicatorVariants({ status, size }),
          pulse && "animate-pulse",
          className
        )}
        {...props}
      >
        {showIcon && getStatusIcon(status, size || "md")}
        <span>{label}</span>
      </div>
    )
  }
)

StatusIndicator.displayName = "StatusIndicator"

export { StatusIndicator, statusIndicatorVariants } 