
"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Settings as SettingsIcon, HelpCircle, Users, FileText, CalendarCheck2, ClipboardList } from "lucide-react";
import { APP_NAME } from "@/lib/constants";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { EmailSettings } from "@/components/settings/EmailSettings";
import { NotificationPreferences } from "@/components/settings/NotificationPreferences";

export default function SettingsPage() {
  const [notificationsEnabled, setNotificationsEnabled] = useState(false);

  useEffect(() => {
    const storedNotifications = localStorage.getItem('notificationsEnabled');
    if (storedNotifications === 'true') {
        setNotificationsEnabled(true);
    }
  }, []);

  const toggleNotifications = () => {
    const newNotificationsEnabled = !notificationsEnabled;
    setNotificationsEnabled(newNotificationsEnabled);
    localStorage.setItem('notificationsEnabled', String(newNotificationsEnabled));
    // In a real app, you would also make an API call here to save this preference on the server.
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex items-center gap-3 mb-6">
        <SettingsIcon className="h-10 w-10 text-primary" />
        <div>
            <h1 className="text-3xl font-bold text-primary">إعدادات البرنامج</h1>
            <p className="text-muted-foreground">قم بتخصيص تفضيلات برنامج {APP_NAME}.</p>
        </div>
      </div>

      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle>تفضيلات الواجهة</CardTitle>
          <CardDescription>تحكم في مظهر وسلوك واجهة المستخدم.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between space-x-2 space-x-reverse border p-4 rounded-lg">
            <Label htmlFor="dark-mode" className="flex flex-col space-y-1 cursor-pointer">
              <span>الوضع الداكن</span>
              <span className="font-normal leading-snug text-muted-foreground">
                قم بتمكين أو تعطيل المظهر الداكن للتطبيق.
              </span>
            </Label>
            <ThemeToggle /> {/* Use the dedicated ThemeToggle component */}
          </div>

          <div className="flex items-center justify-between space-x-2 space-x-reverse border p-4 rounded-lg">
            <Label htmlFor="notifications" className="flex flex-col space-y-1 cursor-pointer">
              <span>الإشعارات (إعداد قديم)</span>
              <span className="font-normal leading-snug text-muted-foreground">
                استخدم قسم "تفضيلات الإشعارات" أدناه للتحكم الكامل في الإشعارات
              </span>
            </Label>
            <Switch
              id="notifications"
              checked={notificationsEnabled}
              onCheckedChange={toggleNotifications}
              aria-label="تبديل الإشعارات"
            />
          </div>
        </CardContent>
      </Card>

      <Separator />

      {/* Notification Preferences */}
      <NotificationPreferences />

      <Separator />

      {/* Email Settings */}
      <EmailSettings />

      <Separator />

      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle>إعدادات الحساب</CardTitle>
          <CardDescription>إدارة معلومات حسابك الشخصي.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="username">اسم المستخدم</Label>
            <Input id="username" defaultValue="اسم المستخدم الحالي" className="mt-1" />
          </div>
          <div>
            <Label htmlFor="email">البريد الإلكتروني</Label>
            <Input id="email" type="email" defaultValue="<EMAIL>" className="mt-1" />
          </div>
          <Button variant="outline">تغيير كلمة المرور</Button>
        </CardContent>
      </Card>

      <Separator />

      <Card className="shadow-lg">
        <CardHeader className="flex flex-row items-center gap-2">
          <HelpCircle className="h-6 w-6 text-primary" />
          <CardTitle>طريقة الاستخدام</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-muted-foreground">
          <div className="space-y-2">
            <p><strong><Users className="inline h-5 w-5 mr-1 text-accent" /> لوحة التحكم:</strong> تعرض ملخصًا لبيانات الأطفال، إحصائيات عامة (مثل إجمالي العدد، التوزيع حسب الجنس، الأطفال الجدد، ومن انتهت خدماتهم)، توزيع الأعمار، ونظرة عامة على تقدم الأهداف. كما تعرض "التنبيهات والإشعارات" الهامة (مثل مواعيد إعادة التقييم، الحاجة لتقييم أولي، واكتمال الخدمة) و "النشاط الأخير" (مثل الأطفال المضافين حديثًا والتقييمات المكتملة مؤخرًا).</p>
          </div>
          <div className="space-y-2">
            <p><strong><Users className="inline h-5 w-5 mr-1 text-accent" /> إدارة الأطفال:</strong></p>
            <ul className="list-disc pr-6 space-y-1 text-sm">
                <li>يمكنك إضافة أطفال جدد (مع تفاصيل مثل الاسم، تاريخ الميلاد، تاريخ الالتحاق، الأخصائي، الجنس، والصورة الشخصية).</li>
                <li>تعديل بيانات الأطفال الحاليين.</li>
                <li>حذف الأطفال (مع تأكيد).</li>
                <li>النقر على اسم الطفل لعرض ملفه الشخصي المفصل.</li>
            </ul>
          </div>

          <div className="space-y-2">
            <p><strong><FileText className="inline h-5 w-5 mr-1 text-accent" /> ملف الطفل:</strong></p>
            <ul className="list-disc pr-6 space-y-1 text-sm">
                <li>يعرض تفاصيل الطفل (الاسم، الصورة، العمر، تاريخ الميلاد، تاريخ الالتحاق، الأخصائي، والجنس).</li>
                <li>يعرض تنبيهات حول إعادة التقييم أو اكتمال الخدمة.</li>
                <li>يتيح "تعديل الملف الشخصي" للطفل.</li>
                <li>قسم "التقييمات": روابط لـ "بدء تقييم جديد" و "عرض سجل التقييمات".</li>
                <li>قسم "التقرير الشامل": رابط للانتقال إلى صفحة إنشاء وعرض التقارير.</li>
                <li>قسم "توثيق الجلسات / محاضر الاجتماعات": لإضافة وعرض محاضر الجلسات مع الأسرة (التاريخ، الهدف المناقش، الحضور، الملاحظات، والخطوات التالية).</li>
                <li>قسم "دراسة الحالة": لإضافة وعرض دراسة حالة مفصلة للطفل (معلومات أساسية، معلومات عن الحمل والولادة، واستجابة الطفل للمعززات) مع إمكانية طباعتها.</li>
            </ul>
          </div>
          
          <div className="space-y-2">
            <p><strong><CalendarCheck2 className="inline h-5 w-5 mr-1 text-accent" /> التقييمات:</strong></p>
            <ul className="list-disc pr-6 space-y-1 text-sm">
              <li><strong>إنشاء تقييم جديد:</strong> من ملف الطفل، اختر "بدء تقييم جديد". املأ قائمة بورتيج المفلترة (حسب البعد، المجال، والفئة العمرية) وحدد حالة كل مهارة مع إضافة ملاحظات إذا لزم الأمر، ثم احفظ التقييم.</li>
              <li><strong>عرض سجل التقييمات:</strong> يعرض قائمة بجميع التقييمات السابقة للطفل.</li>
              <li><strong>تفاصيل التقييم:</strong> بعد اختيار تقييم معين، يمكنك:
                <ul className="list-disc pr-6 space-y-1 mt-1">
                  <li>مراجعة المهارات المقيمة وحالتها والملاحظات عليها.</li>
                  <li>عرض تحليل الخط القاعدي، الخط السقفي، ومنطقة التعلم لكل مجال فرعي.</li>
                  <li>استخدام زر "خطة اسرية" لتوليد اقتراحات من الذكاء الاصطناعي لدمج المهارة في الروتين اليومي للأسرة، مع إمكانية طباعة الخطة.</li>
                  <li>استخدام زر "خطة الروضة / الحضانة" لتوليد اقتراحات لدمج المهارة في روتين الحضانة، مع إمكانية طباعة الخطة.</li>
                  <li>عرض معلومات تتبع التقدم للمهارات (الحالة، تواريخ البدء والإنجاز المستهدف، ملاحظات التقدم) مع تنبيه مرئي للمواعيد المتجاوزة.</li>
                  <li>"تعديل التقييم" لإجراء تغييرات على تقييم محفوظ.</li>
                </ul>
              </li>
            </ul>
          </div>
          
          <div className="space-y-2">
            <p><strong><ClipboardList className="inline h-5 w-5 mr-1 text-accent" /> التقرير الشامل:</strong></p>
            <ul className="list-disc pr-6 space-y-1 text-sm">
              <li>يتم الوصول إليه من ملف الطفل.</li>
              <li>يعرض تحليلًا مفصلاً لآخر تقييم (الخط القاعدي، الخط السقفي، منطقة التعلم).</li>
              <li>يمكنك "إنشاء تقرير شامل" بواسطة الذكاء الاصطناعي بناءً على أحدث تقييم، مع إمكانية إضافة تركيز إضافي للتحليل.</li>
              <li>التقرير المُنشأ يتضمن ملخصًا تنفيذيًا، نقاط القوة، مجالات التطوير، تحليل بيانات، وتوصيات عملية.</li>
              <li>يتم عرض رسم بياني يوضح أداء المهارات حسب البُعد.</li>
              <li>يمكنك نسخ أو طباعة التقرير الشامل.</li>
            </ul>
          </div>
          
          <p><strong><Users className="inline h-5 w-5 mr-1 text-accent" /> إدارة المستخدمين:</strong> من قسم "إدارة المستخدمين"، يمكنك إضافة مستخدمين جدد (مع الاسم، البريد الإلكتروني، التخصص، الدور، والصورة الشخصية)، تعديل بياناتهم وصلاحياتهم، أو حذفهم.</p>
          <p><strong><SettingsIcon className="inline h-5 w-5 mr-1 text-accent" /> إدارة البيانات:</strong> تتيح لك تصدير بيانات التطبيق (الأطفال، التقييمات، المستخدمين) كملف JSON، واستيرادها مرة أخرى (مع العلم أن الاستيراد سيستبدل البيانات الحالية).</p>
          <p><strong><SettingsIcon className="inline h-5 w-5 mr-1 text-accent" /> الإعدادات:</strong> تتيح لك تخصيص تفضيلات البرنامج مثل "الوضع الداكن".</p>
        </CardContent>
      </Card>
      
      <Separator />

       <Card className="shadow-lg">
        <CardHeader>
          <CardTitle>حول البرنامج</CardTitle>
        </CardHeader>
        <CardContent>
            <p className="text-muted-foreground">{APP_NAME} - الإصدار 1.0.0</p>
            <p className="text-muted-foreground">أداة شاملة لتقييم وتنمية الطفولة المبكرة.</p>
            <p className="mt-4">تصميم: احمد الخمايسه</p>
        </CardContent>
      </Card>

    </div>
  );
}

    