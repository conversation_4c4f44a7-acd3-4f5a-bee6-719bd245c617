
'use server';

/**
 * @fileOverview يقوم بإنشاء تقرير شامل ومخصص لطفل بناءً على نتائج التقييم وتحليل البيانات.
 *
 * - generateComprehensiveReport - دالة تقوم بإنشاء التقرير الشامل.
 * - GenerateComprehensiveReportInput - نوع الإدخال لدالة generateComprehensiveReport.
 * - GenerateComprehensiveReportOutput - نوع الإرجاع لدالة generateComprehensiveReport.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const GenerateComprehensiveReportInputSchema = z.object({
  assessmentResults: z
    .string()
    .describe('ملخص لنتائج تقييم الطفل بما في ذلك المهارات المتقنة والتي تحتاج إلى تطوير وأي ملاحظات مهمة.'),
  zoneOfProximalDevelopment: z
    .string()
    .describe(
      'وصف لمنطقة التطور القريبة للطفل، والتي تشير إلى المهارات التي يكون الطفل مستعدًا لتعلمها بالتوجيه.'
    ),
  childName: z.string().describe('اسم الطفل.'),
  ageInMonths: z.number().describe('عمر الطفل بالأشهر.'),
  additionalFocus: z.string().optional().describe('أي معلومات أو توجيهات إضافية للمحلل ليأخذها في الاعتبار عند إنشاء التقرير.'),
});
export type GenerateComprehensiveReportInput = z.infer<
  typeof GenerateComprehensiveReportInputSchema
>;

const GenerateComprehensiveReportOutputSchema = z.object({
  executiveSummary: z.string().describe('ملخص تنفيذي موجز للحالة التنموية الحالية للطفل.'),
  strengths: z.string().describe('وصف لنقاط القوة الملحوظة لدى الطفل بناءً على التقييم.'),
  areasForDevelopment: z.string().describe('تحديد المجالات التي تحتاج إلى مزيد من الدعم والتطوير.'),
  dataAnalysisHighlights: z.string().describe('أبرز الاستنتاجات والرؤى المستخلصة من تحليل بيانات التقييم، مثل الأنماط أو العلاقات بين المهارات المختلفة.'),
  actionableRecommendations: z.string().describe('توصيات عملية ومحددة يمكن للأهل أو مقدمي الرعاية تطبيقها لدعم نمو الطفل.'),
});
export type GenerateComprehensiveReportOutput = z.infer<
  typeof GenerateComprehensiveReportOutputSchema
>;

export async function generateComprehensiveReport(
  input: GenerateComprehensiveReportInput
): Promise<GenerateComprehensiveReportOutput> {
  return generateComprehensiveReportFlow(input);
}

const prompt = ai.definePrompt({
  name: 'generateComprehensiveReportPrompt',
  input: {schema: GenerateComprehensiveReportInputSchema},
  output: {schema: GenerateComprehensiveReportOutputSchema},
  prompt: `أنت خبير في تحليل بيانات تقييم نمو الطفولة المبكرة ومتخصص في إعداد تقارير شاملة وقابلة للتنفيذ بناءً على منهج بورتيج.
مهمتك هي تحليل البيانات المقدمة عن الطفل "{{{childName}}}" (العمر: {{{ageInMonths}}} شهرًا) وإنشاء تقرير مفصل.

البيانات المتاحة:
1.  **ملخص نتائج التقييم:**
    {{{assessmentResults}}}

2.  **منطقة التطور القريبة (ZPD):**
    {{{zoneOfProximalDevelopment}}}

3.  **تركيز إضافي من المستخدم (إن وجد):**
    {{#if additionalFocus}}
    {{{additionalFocus}}}
    {{else}}
    لا يوجد تركيز إضافي.
    {{/if}}

الرجاء إنشاء التقرير الشامل بحيث يتضمن الأقسام التالية:

*   **الملخص التنفيذي:** قدم نظرة عامة موجزة ومكثفة عن الوضع النمائي الحالي للطفل.
*   **نقاط القوة:** استعرض المهارات التي يتقنها الطفل والجوانب الإيجابية التي ظهرت في التقييم.
*   **مجالات التطوير:** حدد المهارات التي تحتاج إلى دعم وتركيز، ووضح أهميتها في سياق النمو الشامل للطفل.
*   **أبرز نتائج تحليل البيانات:** هذا قسم بالغ الأهمية. قم بتحليل معمق للبيانات المقدمة (نتائج التقييم، ZPD). ابحث عن أنماط، أو ارتباطات بين أداء الطفل في مجالات مختلفة، أو أي ملاحظات تحليلية مهمة. على سبيل المثال: "يُظهر الطفل قوة واضحة في المهارات الحركية الدقيقة، بينما تحتاج مهارات التفاعل الاجتماعي إلى دعم أكبر." أو "غالبية المهارات في الفئة العمرية X للتواصل لم يتم إتقانها بعد، مما يشير إلى أهمية التركيز على هذا الجانب." أو "تحليل منطقة النمو القريبة يشير إلى استعداد الطفل للتعامل مع مهام حل المشكلات الأكثر تعقيدًا." يجب أن يكون هذا التحليل أكثر من مجرد سرد للمهارات، بل تفسير واستنتاج.
*   **توصيات عملية:** بناءً على التحليل، قدم توصيات واضحة، محددة، وقابلة للتطبيق يمكن للأهل والمعلمين استخدامها لدعم تطور الطفل في مختلف المجالات. يجب أن تكون هذه التوصيات عملية وموجهة نحو الأنشطة اليومية والبيئة المحيطة بالطفل.

اجعل التقرير منظماً وسهل القراءة.
`,
});

const generateComprehensiveReportFlow = ai.defineFlow(
  {
    name: 'generateComprehensiveReportFlow',
    inputSchema: GenerateComprehensiveReportInputSchema,
    outputSchema: GenerateComprehensiveReportOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    if (!output) {
        throw new Error("لم يتمكن الذكاء الاصطناعي من إنشاء التقرير الشامل.");
    }
    return output;
  }
);

// Ensure the file is renamed to generate-comprehensive-report.ts
// Old name: generate-learning-plan.ts
