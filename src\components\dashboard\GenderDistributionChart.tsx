"use client";

import { <PERSON>, LineC<PERSON>, CartesianGrid, XAxis, YAxis } from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
  type ChartConfig
} from "@/components/ui/chart";

interface GenderDistributionChartProps {
  maleCount: number;
  femaleCount: number;
}

export default function GenderDistributionChart({ maleCount, femaleCount }: GenderDistributionChartProps) {
  const total = maleCount + femaleCount;
  
  if (total === 0) {
    return <p className="text-muted-foreground text-center py-4">لا توجد بيانات كافية لعرض توزيع الجنس.</p>;
  }

  const chartData = [
    { 
      category: 'ذكور', 
      count: maleCount, 
      percentage: Math.round((maleCount / total) * 100)
    },
    { 
      category: 'إناث', 
      count: femaleCount, 
      percentage: Math.round((femaleCount / total) * 100)
    },
  ];

  const chartConfig = {
    count: { label: "العدد", color: "hsl(var(--chart-1))" },
    percentage: { label: "النسبة المئوية", color: "hsl(var(--chart-2))" },
  } satisfies ChartConfig;

  return (
    <div className="h-[300px] w-full">
      <ChartContainer config={chartConfig} className="w-full h-full">
        <LineChart 
          data={chartData} 
          margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
          <XAxis 
            dataKey="category" 
            stroke="hsl(var(--muted-foreground))" 
            fontSize={12}
          />
          <YAxis 
            stroke="hsl(var(--muted-foreground))" 
            fontSize={12}
            allowDecimals={false}
          />
          <ChartTooltip content={<ChartTooltipContent />} />
          <ChartLegend content={<ChartLegendContent />} />
          <Line
            type="monotone"
            dataKey="count"
            stroke="var(--color-count)"
            strokeWidth={4}
            dot={{ fill: "var(--color-count)", strokeWidth: 2, r: 6 }}
            activeDot={{ r: 8, fill: "var(--color-count)" }}
          />
          <Line
            type="monotone"
            dataKey="percentage"
            stroke="var(--color-percentage)"
            strokeWidth={4}
            strokeDasharray="8 8"
            dot={{ fill: "var(--color-percentage)", strokeWidth: 2, r: 6 }}
            activeDot={{ r: 8, fill: "var(--color-percentage)" }}
          />
        </LineChart>
      </ChartContainer>
    </div>
  );
}

