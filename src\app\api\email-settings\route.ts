import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for email settings
const emailSettingsSchema = z.object({
  smtpHost: z.string().optional(),
  smtpPort: z.number().min(1).max(65535).optional(),
  smtpSecure: z.boolean().optional(),
  smtpUser: z.string().optional(),
  smtpPassword: z.string().optional(),
  fromEmail: z.string().email().optional(),
  fromName: z.string().optional(),
  isEnabled: z.boolean().optional(),
});

// GET /api/email-settings - Get email settings (admin only)
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (user?.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 });
    }

    let settings = await prisma.emailSettings.findFirst();

    // Create default settings if they don't exist
    if (!settings) {
      settings = await prisma.emailSettings.create({
        data: {
          smtpPort: 587,
          smtpSecure: false,
          isEnabled: false,
        },
      });
    }

    // Don't return sensitive data like password
    const { smtpPassword, ...safeSettings } = settings;

    return NextResponse.json({
      ...safeSettings,
      hasPassword: !!smtpPassword,
    });
  } catch (error) {
    console.error('Error fetching email settings:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/email-settings - Update email settings (admin only)
export async function PUT(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (user?.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = emailSettingsSchema.parse(body);

    // Remove empty password field to avoid overwriting existing password
    if (validatedData.smtpPassword === '') {
      delete validatedData.smtpPassword;
    }

    const settings = await prisma.emailSettings.upsert({
      where: { id: 'default' },
      update: validatedData,
      create: {
        id: 'default',
        smtpPort: 587,
        smtpSecure: false,
        isEnabled: false,
        ...validatedData,
      },
    });

    // Don't return sensitive data like password
    const { smtpPassword, ...safeSettings } = settings;

    return NextResponse.json({
      ...safeSettings,
      hasPassword: !!smtpPassword,
    });
  } catch (error) {
    console.error('Error updating email settings:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
