"use client"

import * as React from "react"
import { Bell, Search as SearchIcon, Users, FileText, Calendar } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { SidebarTrigger } from "@/components/ui/sidebar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useChildren, useAssessments } from "@/hooks/use-storage"
import { calculateAge, formatDate } from "@/lib/utils"
import { differenceInDays, parseISO, addMonths, isPast, isValid } from "date-fns"
import Link from "next/link"
import { NotificationBell } from "@/components/notifications/NotificationBell"

export function SiteHeader() {
  const [searchOpen, setSearchOpen] = React.useState(false)

  const [searchValue, setSearchValue] = React.useState("")
  
  const { children, loading: childrenLoading } = useChildren()
  const { assessments, loading: assessmentsLoading } = useAssessments()

  // Helper to get the latest assessment for a child
  const getLatestAssessmentForChild = (childId: string) => {
    return assessments
      .filter(a => a.childId === childId)
      .sort((a, b) => new Date(b.assessmentDate).getTime() - new Date(a.assessmentDate).getTime())[0]
  }



  // Search items
  const searchItems = React.useMemo(() => {
    if (childrenLoading) return []
    
    const items: Array<{
      id: string
      label: string
      value: string
      group: string
      href: string
      icon: any
    }> = []
    
    // Add children to search
    children.forEach(child => {
      items.push({
        id: `child-${child.id}`,
        label: child.name,
        value: child.name.toLowerCase(),
        group: "الأطفال",
        href: `/children/${child.id}`,
        icon: Users
      })
    })

    // Add assessments to search
    assessments.forEach(assessment => {
      const child = children.find(c => c.id === assessment.childId)
      if (child) {
        items.push({
          id: `assessment-${assessment.id}`,
          label: `تقييم ${child.name} - ${formatDate(assessment.assessmentDate)}`,
          value: `تقييم ${child.name}`.toLowerCase(),
          group: "التقييمات",
          href: `/children/${assessment.childId}/assessment/${assessment.id}`,
          icon: FileText
        })
      }
    })

    return items
  }, [children, assessments, childrenLoading])

  const filteredItems = searchItems.filter(item =>
    item.value.includes(searchValue.toLowerCase())
  )



  return (
    <header className="group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 flex h-12 shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear">
      <div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
        <SidebarTrigger className="-ml-1" />
        <Separator
          orientation="vertical"
          className="mx-2 data-[orientation=vertical]:h-4"
        />
        <h1 className="text-base font-medium">لوحة التحكم</h1>
        
        {/* Search */}
        <div className="flex-1 max-w-md mx-4">
          <Popover open={searchOpen} onOpenChange={setSearchOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={searchOpen}
                className="w-full justify-start text-muted-foreground"
              >
                <SearchIcon className="mr-2 h-4 w-4" />
                البحث في الأطفال والتقييمات...
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-0" align="start">
              <Command>
                <CommandInput 
                  placeholder="البحث..." 
                  value={searchValue}
                  onValueChange={setSearchValue}
                />
                <CommandList>
                  <CommandEmpty>لا توجد نتائج.</CommandEmpty>
                  {filteredItems.length > 0 && (
                    <>
                      {/* Group by category */}
                      {["الأطفال", "التقييمات"].map(group => {
                        const groupItems = filteredItems.filter(item => item.group === group)
                        if (groupItems.length === 0) return null
                        
                        return (
                          <CommandGroup key={group} heading={group}>
                            {groupItems.map((item) => (
                              <CommandItem
                                key={item.id}
                                onSelect={() => {
                                  setSearchOpen(false)
                                  window.location.href = item.href
                                }}
                              >
                                <item.icon className="mr-2 h-4 w-4" />
                                {item.label}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        )
                      })}
                    </>
                  )}
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        </div>

        {/* Notifications */}
        <NotificationBell />
      </div>
    </header>
  )
}
