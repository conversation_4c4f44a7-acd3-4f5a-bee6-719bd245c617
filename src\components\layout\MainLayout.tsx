"use client";

import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>bar<PERSON>ontent,
  <PERSON>bar<PERSON>nse<PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>barFooter,
} from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Brain, Sparkles } from 'lucide-react';
import AppHeader from '@/components/layout/AppHeader';
import AppSidebar from '@/components/layout/AppSidebar';
import { APP_NAME } from '@/lib/constants';

type MainLayoutProps = {
  children: React.ReactNode;
};

export default function MainLayout({ children }: MainLayoutProps) {
  return (
    <>
      <Sidebar 
        side="right" 
        className="border-l-0 bg-sidebar-background backdrop-blur supports-[backdrop-filter]:bg-sidebar-background/95"
        collapsible="icon"
      >
        <SidebarHeader className="border-b border-sidebar-border/40 p-4">
          <div className="flex items-center gap-3 group-data-[collapsible=icon]:justify-center">
            <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-primary to-primary/80 text-primary-foreground shadow-sm">
              <Brain className="h-5 w-5" />
            </div>
            <div className="flex flex-col group-data-[collapsible=icon]:hidden">
              <div className="flex items-center gap-2">
                <span className="text-lg font-bold text-sidebar-foreground">
                  {APP_NAME}
                </span>
                <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                  v1.0
                </Badge>
              </div>
              <p className="text-xs text-sidebar-foreground/60">
                تنمية الطفولة المبكرة
              </p>
            </div>
          </div>
        </SidebarHeader>
        
        <SidebarContent className="px-0">
          <AppSidebar />
        </SidebarContent>
        
        <SidebarFooter className="border-t border-sidebar-border/40 p-4">
          <div className="group-data-[collapsible=icon]:hidden">
            <div className="flex items-center gap-2 text-xs text-sidebar-foreground/60 mb-2">
              <Sparkles className="h-3 w-3" />
              <span>Pine Team</span>
            </div>
            <div className="text-xs text-sidebar-foreground/40">
              النسخة 1.0.0 • 2024
            </div>
          </div>
          <div className="group-data-[collapsible=icon]:block hidden">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-sidebar-accent text-sidebar-accent-foreground">
              <Sparkles className="h-4 w-4" />
            </div>
          </div>
        </SidebarFooter>
      </Sidebar>
      
      <div 
        className="layout-main flex flex-col min-h-screen bg-background"
        style={{
          marginRight: 'var(--sidebar-width)',
          width: 'calc(100vw - var(--sidebar-width))'
        }}
      >
        <AppHeader />
        
        <main className="flex-1 overflow-auto">
          <div className="container mx-auto p-4 sm:p-6 lg:p-8 max-w-7xl">
            <div className="min-h-[calc(100vh-8rem)]">
              {children}
            </div>
          </div>
        </main>
        
        <footer className="border-t border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <div className="flex items-center gap-4">
                <span>© 2024 {APP_NAME}</span>
                <Separator orientation="vertical" className="h-4" />
                <span>جميع الحقوق محفوظة</span>
              </div>
              <div className="flex items-center gap-2">
                <Sparkles className="h-3 w-3" />
                <span>Pine Team</span>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
